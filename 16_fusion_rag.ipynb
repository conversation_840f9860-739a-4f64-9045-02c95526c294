{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["# Fusion Retrieval: Combining Vector and Keyword Search\n", "\n", "In this notebook, I implement a fusion retrieval system that combines the strengths of semantic vector search with keyword-based BM25 retrieval. This approach improves retrieval quality by capturing both conceptual similarity and exact keyword matches.\n", "\n", "## Why Fusion Retrieval Matters\n", "\n", "Traditional RAG systems typically rely on vector search alone, but this has limitations:\n", "\n", "- Vector search excels at semantic similarity but may miss exact keyword matches\n", "- Keyword search is great for specific terms but lacks semantic understanding\n", "- Different queries perform better with different retrieval methods\n", "\n", "Fusion retrieval gives us the best of both worlds by:\n", "\n", "- Performing both vector-based and keyword-based retrieval\n", "- Normalizing the scores from each approach\n", "- Combining them with a weighted formula\n", "- Ranking documents based on the combined score"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "We begin by importing necessary libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "from rank_bm25 import BM25Okapi\n", "import fitz\n", "from openai import OpenAI\n", "import re\n", "import json\n", "import time\n", "from sklearn.metrics.pairwise import cosine_similarity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the OpenAI API Client\n", "We initialize the OpenAI client to generate embeddings and responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the OpenAI client with the base URL and API key\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",\n", "    api_key=os.getenv(\"OPENAI_API_KEY\")  # Retrieve the API key from environment variables\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Processing Functions"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def extract_text_from_pdf(pdf_path):\n", "    \"\"\"\n", "    Extract text content from a PDF file.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        \n", "    Returns:\n", "        str: Extracted text content\n", "    \"\"\"\n", "    print(f\"Extracting text from {pdf_path}...\")  # Print the path of the PDF being processed\n", "    pdf_document = fitz.open(pdf_path)  # Open the PDF file using PyMuPDF\n", "    text = \"\"  # Initialize an empty string to store the extracted text\n", "    \n", "    # Iterate through each page in the PDF\n", "    for page_num in range(pdf_document.page_count):\n", "        page = pdf_document[page_num]  # Get the page object\n", "        text += page.get_text()  # Extract text from the page and append to the text string\n", "    \n", "    return text  # Return the extracted text content"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def chunk_text(text, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Split text into overlapping chunks.\n", "    \n", "    Args:\n", "        text (str): Input text to chunk\n", "        chunk_size (int): Size of each chunk in characters\n", "        chunk_overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        List[Dict]: List of chunks with text and metadata\n", "    \"\"\"\n", "    chunks = []  # Initialize an empty list to store chunks\n", "    \n", "    # Iterate over the text with the specified chunk size and overlap\n", "    for i in range(0, len(text), chunk_size - chunk_overlap):\n", "        chunk = text[i:i + chunk_size]  # Extract a chunk of the specified size\n", "        if chunk:  # Ensure we don't add empty chunks\n", "            chunk_data = {\n", "                \"text\": chunk,  # The chunk text\n", "                \"metadata\": {\n", "                    \"start_char\": i,  # Start character index of the chunk\n", "                    \"end_char\": i + len(chunk)  # End character index of the chunk\n", "                }\n", "            }\n", "            chunks.append(chunk_data)  # Add the chunk data to the list\n", "    \n", "    print(f\"Created {len(chunks)} text chunks\")  # Print the number of created chunks\n", "    return chunks  # Return the list of chunks"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def clean_text(text):\n", "    \"\"\"\n", "    Clean text by removing extra whitespace and special characters.\n", "    \n", "    Args:\n", "        text (str): Input text\n", "        \n", "    Returns:\n", "        str: Cleaned text\n", "    \"\"\"\n", "    # Replace multiple whitespace characters (including newlines and tabs) with a single space\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    \n", "    # Fix common OCR issues by replacing tab and newline characters with a space\n", "    text = text.replace('\\\\t', ' ')\n", "    text = text.replace('\\\\n', ' ')\n", "    \n", "    # Remove any leading or trailing whitespace and ensure single spaces between words\n", "    text = ' '.join(text.split())\n", "    \n", "    return text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Our Vector Store"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def create_embeddings(texts, model=\"BAAI/bge-en-icl\"):\n", "    \"\"\"\n", "    Create embeddings for the given texts.\n", "    \n", "    Args:\n", "        texts (str or List[str]): Input text(s)\n", "        model (str): Embedding model name\n", "        \n", "    Returns:\n", "        List[List[float]]: Embedding vectors\n", "    \"\"\"\n", "    # Handle both string and list inputs\n", "    input_texts = texts if isinstance(texts, list) else [texts]\n", "    \n", "    # Process in batches if needed (OpenAI API limits)\n", "    batch_size = 100\n", "    all_embeddings = []\n", "    \n", "    # Iterate over the input texts in batches\n", "    for i in range(0, len(input_texts), batch_size):\n", "        batch = input_texts[i:i + batch_size]  # Get the current batch of texts\n", "        \n", "        # Create embeddings for the current batch\n", "        response = client.embeddings.create(\n", "            model=model,\n", "            input=batch\n", "        )\n", "        \n", "        # Extract embeddings from the response\n", "        batch_embeddings = [item.embedding for item in response.data]\n", "        all_embeddings.extend(batch_embeddings)  # Add the batch embeddings to the list\n", "    \n", "    # If input was a string, return just the first embedding\n", "    if isinstance(texts, str):\n", "        return all_embeddings[0]\n", "    \n", "    # Otherwise return all embeddings\n", "    return all_embeddings"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["class SimpleVectorStore:\n", "    \"\"\"\n", "    A simple vector store implementation using NumPy.\n", "    \"\"\"\n", "    def __init__(self):\n", "        self.vectors = []  # List to store embedding vectors\n", "        self.texts = []  # List to store text content\n", "        self.metadata = []  # List to store metadata\n", "    \n", "    def add_item(self, text, embedding, metadata=None):\n", "        \"\"\"\n", "        Add an item to the vector store.\n", "        \n", "        Args:\n", "            text (str): The text content\n", "            embedding (List[float]): The embedding vector\n", "            metadata (Dict, optional): Additional metadata\n", "        \"\"\"\n", "        self.vectors.append(np.array(embedding))  # Append the embedding vector\n", "        self.texts.append(text)  # Append the text content\n", "        self.metadata.append(metadata or {})  # Append the metadata (or empty dict if None)\n", "    \n", "    def add_items(self, items, embeddings):\n", "        \"\"\"\n", "        Add multiple items to the vector store.\n", "        \n", "        Args:\n", "            items (List[Dict]): List of text items\n", "            embeddings (List[List[float]]): List of embedding vectors\n", "        \"\"\"\n", "        for i, (item, embedding) in enumerate(zip(items, embeddings)):\n", "            self.add_item(\n", "                text=item[\"text\"],  # Extract text from item\n", "                embedding=embedding,  # Use corresponding embedding\n", "                metadata={**item.get(\"metadata\", {}), \"index\": i}  # Merge item metadata with index\n", "            )\n", "    \n", "    def similarity_search_with_scores(self, query_embedding, k=5):\n", "        \"\"\"\n", "        Find the most similar items to a query embedding with similarity scores.\n", "        \n", "        Args:\n", "            query_embedding (List[float]): Query embedding vector\n", "            k (int): Number of results to return\n", "            \n", "        Returns:\n", "            List[Tuple[Dict, float]]: Top k most similar items with scores\n", "        \"\"\"\n", "        if not self.vectors:\n", "            return []  # Return empty list if no vectors are stored\n", "        \n", "        # Convert query embedding to numpy array\n", "        query_vector = np.array(query_embedding)\n", "        \n", "        # Calculate similarities using cosine similarity\n", "        similarities = []\n", "        for i, vector in enumerate(self.vectors):\n", "            similarity = cosine_similarity([query_vector], [vector])[0][0]  # Compute cosine similarity\n", "            similarities.append((i, similarity))  # Append index and similarity score\n", "        \n", "        # Sort by similarity (descending)\n", "        similarities.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        # Return top k results with scores\n", "        results = []\n", "        for i in range(min(k, len(similarities))):\n", "            idx, score = similarities[i]\n", "            results.append({\n", "                \"text\": self.texts[idx],  # Retrieve text by index\n", "                \"metadata\": self.metadata[idx],  # Retrieve metadata by index\n", "                \"similarity\": float(score)  # Add similarity score\n", "            })\n", "        \n", "        return results\n", "    \n", "    def get_all_documents(self):\n", "        \"\"\"\n", "        Get all documents in the store.\n", "        \n", "        Returns:\n", "            List[Dict]: All documents\n", "        \"\"\"\n", "        return [{\"text\": text, \"metadata\": meta} for text, meta in zip(self.texts, self.metadata)]  # Combine texts and metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## BM25 Implementation"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def create_bm25_index(chunks):\n", "    \"\"\"\n", "    Create a BM25 index from the given chunks.\n", "    \n", "    Args:\n", "        chunks (List[Dict]): List of text chunks\n", "        \n", "    Returns:\n", "        BM25Okapi: A BM25 index\n", "    \"\"\"\n", "    # Extract text from each chunk\n", "    texts = [chunk[\"text\"] for chunk in chunks]\n", "    \n", "    # Tokenize each document by splitting on whitespace\n", "    tokenized_docs = [text.split() for text in texts]\n", "    \n", "    # Create the BM25 index using the tokenized documents\n", "    bm25 = BM25Okapi(tokenized_docs)\n", "    \n", "    # Print the number of documents in the BM25 index\n", "    print(f\"Created BM25 index with {len(texts)} documents\")\n", "    \n", "    return bm25"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def bm25_search(bm25, chunks, query, k=5):\n", "    \"\"\"\n", "    Search the BM25 index with a query.\n", "    \n", "    Args:\n", "        bm25 (BM25Okapi): BM25 index\n", "        chunks (List[Dict]): List of text chunks\n", "        query (str): Query string\n", "        k (int): Number of results to return\n", "        \n", "    Returns:\n", "        List[Dict]: Top k results with scores\n", "    \"\"\"\n", "    # Tokenize the query by splitting it into individual words\n", "    query_tokens = query.split()\n", "    \n", "    # Get BM25 scores for the query tokens against the indexed documents\n", "    scores = bm25.get_scores(query_tokens)\n", "    \n", "    # Initialize an empty list to store results with their scores\n", "    results = []\n", "    \n", "    # Iterate over the scores and corresponding chunks\n", "    for i, score in enumerate(scores):\n", "        # Create a copy of the metadata to avoid modifying the original\n", "        metadata = chunks[i].get(\"metadata\", {}).copy()\n", "        # Add index to metadata\n", "        metadata[\"index\"] = i\n", "        \n", "        results.append({\n", "            \"text\": chunks[i][\"text\"],\n", "            \"metadata\": metadata,  # Add metadata with index\n", "            \"bm25_score\": float(score)\n", "        })\n", "    \n", "    # Sort the results by BM25 score in descending order\n", "    results.sort(key=lambda x: x[\"bm25_score\"], reverse=True)\n", "    \n", "    # Return the top k results\n", "    return results[:k]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fusion Retrieval Function"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def fusion_retrieval(query, chunks, vector_store, bm25_index, k=5, alpha=0.5):\n", "    \"\"\"\n", "    Perform fusion retrieval combining vector-based and BM25 search.\n", "    \n", "    Args:\n", "        query (str): Query string\n", "        chunks (List[Dict]): Original text chunks\n", "        vector_store (SimpleVectorStore): Vector store\n", "        bm25_index (BM25Okapi): BM25 index\n", "        k (int): Number of results to return\n", "        alpha (float): Weight for vector scores (0-1), where 1-alpha is BM25 weight\n", "        \n", "    Returns:\n", "        List[Dict]: Top k results based on combined scores\n", "    \"\"\"\n", "    print(f\"Performing fusion retrieval for query: {query}\")\n", "    \n", "    # Define small epsilon to avoid division by zero\n", "    epsilon = 1e-8\n", "    \n", "    # Get vector search results\n", "    query_embedding = create_embeddings(query)  # Create embedding for the query\n", "    vector_results = vector_store.similarity_search_with_scores(query_embedding, k=len(chunks))  # Perform vector search\n", "    \n", "    # Get BM25 search results\n", "    bm25_results = bm25_search(bm25_index, chunks, query, k=len(chunks))  # Perform BM25 search\n", "    \n", "    # Create dictionaries to map document index to score\n", "    vector_scores_dict = {result[\"metadata\"][\"index\"]: result[\"similarity\"] for result in vector_results}\n", "    bm25_scores_dict = {result[\"metadata\"][\"index\"]: result[\"bm25_score\"] for result in bm25_results}\n", "    \n", "    # Ensure all documents have scores for both methods\n", "    all_docs = vector_store.get_all_documents()\n", "    combined_results = []\n", "    \n", "    for i, doc in enumerate(all_docs):\n", "        vector_score = vector_scores_dict.get(i, 0.0)  # Get vector score or 0 if not found\n", "        bm25_score = bm25_scores_dict.get(i, 0.0)  # Get BM25 score or 0 if not found\n", "        combined_results.append({\n", "            \"text\": doc[\"text\"],\n", "            \"metadata\": doc[\"metadata\"],\n", "            \"vector_score\": vector_score,\n", "            \"bm25_score\": bm25_score,\n", "            \"index\": i\n", "        })\n", "    \n", "    # Extract scores as arrays\n", "    vector_scores = np.array([doc[\"vector_score\"] for doc in combined_results])\n", "    bm25_scores = np.array([doc[\"bm25_score\"] for doc in combined_results])\n", "    \n", "    # Normalize scores\n", "    norm_vector_scores = (vector_scores - np.min(vector_scores)) / (np.max(vector_scores) - np.min(vector_scores) + epsilon)\n", "    norm_bm25_scores = (bm25_scores - np.min(bm25_scores)) / (np.max(bm25_scores) - np.min(bm25_scores) + epsilon)\n", "    \n", "    # Compute combined scores\n", "    combined_scores = alpha * norm_vector_scores + (1 - alpha) * norm_bm25_scores\n", "    \n", "    # Add combined scores to results\n", "    for i, score in enumerate(combined_scores):\n", "        combined_results[i][\"combined_score\"] = float(score)\n", "    \n", "    # Sort by combined score (descending)\n", "    combined_results.sort(key=lambda x: x[\"combined_score\"], reverse=True)\n", "    \n", "    # Return top k results\n", "    top_results = combined_results[:k]\n", "    \n", "    print(f\"Retrieved {len(top_results)} documents with fusion retrieval\")\n", "    return top_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Processing Pipeline"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def process_document(pdf_path, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Process a document for fusion retrieval.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        chunk_size (int): Size of each chunk in characters\n", "        chunk_overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        Tuple[List[Dict], SimpleVectorStore, BM25Okapi]: Chunks, vector store, and BM25 index\n", "    \"\"\"\n", "    # Extract text from the PDF file\n", "    text = extract_text_from_pdf(pdf_path)\n", "    \n", "    # Clean the extracted text to remove extra whitespace and special characters\n", "    cleaned_text = clean_text(text)\n", "    \n", "    # Split the cleaned text into overlapping chunks\n", "    chunks = chunk_text(cleaned_text, chunk_size, chunk_overlap)\n", "    \n", "    # Extract the text content from each chunk for embedding creation\n", "    chunk_texts = [chunk[\"text\"] for chunk in chunks]\n", "    print(\"Creating embeddings for chunks...\")\n", "    \n", "    # Create embeddings for the chunk texts\n", "    embeddings = create_embeddings(chunk_texts)\n", "    \n", "    # Initialize the vector store\n", "    vector_store = SimpleVectorStore()\n", "    \n", "    # Add the chunks and their embeddings to the vector store\n", "    vector_store.add_items(chunks, embeddings)\n", "    print(f\"Added {len(chunks)} items to vector store\")\n", "    \n", "    # Create a BM25 index from the chunks\n", "    bm25_index = create_bm25_index(chunks)\n", "    \n", "    # Return the chunks, vector store, and BM25 index\n", "    return chunks, vector_store, bm25_index"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Response Generation"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def generate_response(query, context):\n", "    \"\"\"\n", "    Generate a response based on the query and context.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        context (str): Context from retrieved documents\n", "        \n", "    Returns:\n", "        str: Generated response\n", "    \"\"\"\n", "    # Define the system prompt to guide the AI assistant\n", "    system_prompt = \"\"\"You are a helpful AI assistant. Answer the user's question based on the provided context. \n", "    If the context doesn't contain relevant information to answer the question fully, acknowledge this limitation.\"\"\"\n", "\n", "    # Format the user prompt with the context and query\n", "    user_prompt = f\"\"\"Context:\n", "    {context}\n", "\n", "    Question: {query}\n", "\n", "    Please answer the question based on the provided context.\"\"\"\n", "\n", "    # Generate the response using the OpenAI API\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",  # Specify the model to use\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},  # System message to guide the assistant\n", "            {\"role\": \"user\", \"content\": user_prompt}  # User message with context and query\n", "        ],\n", "        temperature=0.1  # Set the temperature for response generation\n", "    )\n", "    \n", "    # Return the generated response\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main Retrieval Function"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def answer_with_fusion_rag(query, chunks, vector_store, bm25_index, k=5, alpha=0.5):\n", "    \"\"\"\n", "    Answer a query using fusion RAG.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        chunks (List[Dict]): Text chunks\n", "        vector_store (SimpleVectorStore): Vector store\n", "        bm25_index (BM25Okapi): BM25 index\n", "        k (int): Number of documents to retrieve\n", "        alpha (float): Weight for vector scores\n", "        \n", "    Returns:\n", "        Dict: Query results including retrieved documents and response\n", "    \"\"\"\n", "    # Retrieve documents using fusion retrieval method\n", "    retrieved_docs = fusion_retrieval(query, chunks, vector_store, bm25_index, k=k, alpha=alpha)\n", "    \n", "    # Format the context from the retrieved documents by joining their text with separators\n", "    context = \"\\n\\n---\\n\\n\".join([doc[\"text\"] for doc in retrieved_docs])\n", "    \n", "    # Generate a response based on the query and the formatted context\n", "    response = generate_response(query, context)\n", "    \n", "    # Return the query, retrieved documents, and the generated response\n", "    return {\n", "        \"query\": query,\n", "        \"retrieved_documents\": retrieved_docs,\n", "        \"response\": response\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comparing Retrieval Methods"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def vector_only_rag(query, vector_store, k=5):\n", "    \"\"\"\n", "    Answer a query using only vector-based RAG.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        vector_store (SimpleVectorStore): Vector store\n", "        k (int): Number of documents to retrieve\n", "        \n", "    Returns:\n", "        Dict: Query results\n", "    \"\"\"\n", "    # Create query embedding\n", "    query_embedding = create_embeddings(query)\n", "    \n", "    # Retrieve documents using vector-based similarity search\n", "    retrieved_docs = vector_store.similarity_search_with_scores(query_embedding, k=k)\n", "    \n", "    # Format the context from the retrieved documents by joining their text with separators\n", "    context = \"\\n\\n---\\n\\n\".join([doc[\"text\"] for doc in retrieved_docs])\n", "    \n", "    # Generate a response based on the query and the formatted context\n", "    response = generate_response(query, context)\n", "    \n", "    # Return the query, retrieved documents, and the generated response\n", "    return {\n", "        \"query\": query,\n", "        \"retrieved_documents\": retrieved_docs,\n", "        \"response\": response\n", "    }\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def bm25_only_rag(query, chunks, bm25_index, k=5):\n", "    \"\"\"\n", "    Answer a query using only BM25-based RAG.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        chunks (List[Dict]): Text chunks\n", "        bm25_index (BM25Okapi): BM25 index\n", "        k (int): Number of documents to retrieve\n", "        \n", "    Returns:\n", "        Dict: Query results\n", "    \"\"\"\n", "    # Retrieve documents using BM25 search\n", "    retrieved_docs = bm25_search(bm25_index, chunks, query, k=k)\n", "    \n", "    # Format the context from the retrieved documents by joining their text with separators\n", "    context = \"\\n\\n---\\n\\n\".join([doc[\"text\"] for doc in retrieved_docs])\n", "    \n", "    # Generate a response based on the query and the formatted context\n", "    response = generate_response(query, context)\n", "    \n", "    # Return the query, retrieved documents, and the generated response\n", "    return {\n", "        \"query\": query,\n", "        \"retrieved_documents\": retrieved_docs,\n", "        \"response\": response\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation Functions"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def compare_retrieval_methods(query, chunks, vector_store, bm25_index, k=5, alpha=0.5, reference_answer=None):\n", "    \"\"\"\n", "    Compare different retrieval methods for a query.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        chunks (List[Dict]): Text chunks\n", "        vector_store (SimpleVectorStore): Vector store\n", "        bm25_index (BM25Okapi): BM25 index\n", "        k (int): Number of documents to retrieve\n", "        alpha (float): Weight for vector scores in fusion retrieval\n", "        reference_answer (str, optional): Reference answer for comparison\n", "        \n", "    Returns:\n", "        Dict: Comparison results\n", "    \"\"\"\n", "    print(f\"\\n=== Comparing retrieval methods for query: {query} ===\\n\")\n", "    \n", "    # Run vector-only RAG\n", "    print(\"\\nRunning vector-only RAG...\")\n", "    vector_result = vector_only_rag(query, vector_store, k)\n", "    \n", "    # Run BM25-only RAG\n", "    print(\"\\nRunning BM25-only RAG...\")\n", "    bm25_result = bm25_only_rag(query, chunks, bm25_index, k)\n", "    \n", "    # Run fusion RAG\n", "    print(\"\\nRunning fusion RAG...\")\n", "    fusion_result = answer_with_fusion_rag(query, chunks, vector_store, bm25_index, k, alpha)\n", "    \n", "    # Compare responses from different retrieval methods\n", "    print(\"\\nComparing responses...\")\n", "    comparison = evaluate_responses(\n", "        query, \n", "        vector_result[\"response\"], \n", "        bm25_result[\"response\"], \n", "        fusion_result[\"response\"],\n", "        reference_answer\n", "    )\n", "    \n", "    # Return the comparison results\n", "    return {\n", "        \"query\": query,\n", "        \"vector_result\": vector_result,\n", "        \"bm25_result\": bm25_result,\n", "        \"fusion_result\": fusion_result,\n", "        \"comparison\": comparison\n", "    }"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def evaluate_responses(query, vector_response, bm25_response, fusion_response, reference_answer=None):\n", "    \"\"\"\n", "    Evaluate the responses from different retrieval methods.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        vector_response (str): Response from vector-only RAG\n", "        bm25_response (str): Response from BM25-only RAG\n", "        fusion_response (str): Response from fusion RAG\n", "        reference_answer (str, optional): Reference answer\n", "        \n", "    Returns:\n", "        str: Evaluation of responses\n", "    \"\"\"\n", "    # System prompt for the evaluator to guide the evaluation process\n", "    system_prompt = \"\"\"You are an expert evaluator of RAG systems. Compare responses from three different retrieval approaches:\n", "    1. Vector-based retrieval: Uses semantic similarity for document retrieval\n", "    2. BM25 keyword retrieval: Uses keyword matching for document retrieval\n", "    3. Fusion retrieval: Combines both vector and keyword approaches\n", "\n", "    Evaluate the responses based on:\n", "    - Relevance to the query\n", "    - Factual correctness\n", "    - Comprehensiveness\n", "    - Clarity and coherence\"\"\"\n", "\n", "    # User prompt containing the query and responses\n", "    user_prompt = f\"\"\"Query: {query}\n", "\n", "    Vector-based response:\n", "    {vector_response}\n", "\n", "    BM25 keyword response:\n", "    {bm25_response}\n", "\n", "    Fusion response:\n", "    {fusion_response}\n", "    \"\"\"\n", "\n", "    # Add reference answer to the prompt if provided\n", "    if reference_answer:\n", "        user_prompt += f\"\"\"\n", "            Reference answer:\n", "            {reference_answer}\n", "        \"\"\"\n", "\n", "    # Add instructions for detailed comparison to the user prompt\n", "    user_prompt += \"\"\"\n", "    Please provide a detailed comparison of these three responses. Which approach performed best for this query and why?\n", "    Be specific about the strengths and weaknesses of each approach for this particular query.\n", "    \"\"\"\n", "\n", "    # Generate the evaluation using meta-llama/Llama-3.3-70B-Instruct\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",  # Specify the model to use\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},  # System message to guide the evaluator\n", "            {\"role\": \"user\", \"content\": user_prompt}  # User message with query and responses\n", "        ],\n", "        temperature=0  # Set the temperature for response generation\n", "    )\n", "    \n", "    # Return the generated evaluation content\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complete Evaluation Pipeline"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def evaluate_fusion_retrieval(pdf_path, test_queries, reference_answers=None, k=5, alpha=0.5):\n", "    \"\"\"\n", "    Evaluate fusion retrieval compared to other methods.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        test_queries (List[str]): List of test queries\n", "        reference_answers (List[str], optional): Reference answers\n", "        k (int): Number of documents to retrieve\n", "        alpha (float): Weight for vector scores in fusion retrieval\n", "        \n", "    Returns:\n", "        Dict: Evaluation results\n", "    \"\"\"\n", "    print(\"=== EVALUATING FUSION RETRIEVAL ===\\n\")\n", "    \n", "    # Process the document to extract text, create chunks, and build vector and BM25 indices\n", "    chunks, vector_store, bm25_index = process_document(pdf_path)\n", "    \n", "    # Initialize a list to store results for each query\n", "    results = []\n", "    \n", "    # Iterate over each test query\n", "    for i, query in enumerate(test_queries):\n", "        print(f\"\\n\\n=== Evaluating Query {i+1}/{len(test_queries)} ===\")\n", "        print(f\"Query: {query}\")\n", "        \n", "        # Get the reference answer if available\n", "        reference = None\n", "        if reference_answers and i < len(reference_answers):\n", "            reference = reference_answers[i]\n", "        \n", "        # Compare retrieval methods for the current query\n", "        comparison = compare_retrieval_methods(\n", "            query, \n", "            chunks, \n", "            vector_store, \n", "            bm25_index, \n", "            k=k, \n", "            alpha=alpha,\n", "            reference_answer=reference\n", "        )\n", "        \n", "        # Append the comparison results to the results list\n", "        results.append(comparison)\n", "        \n", "        # Print the responses from different retrieval methods\n", "        print(\"\\n=== Vector-based Response ===\")\n", "        print(comparison[\"vector_result\"][\"response\"])\n", "        \n", "        print(\"\\n=== BM25 Response ===\")\n", "        print(comparison[\"bm25_result\"][\"response\"])\n", "        \n", "        print(\"\\n=== Fusion Response ===\")\n", "        print(comparison[\"fusion_result\"][\"response\"])\n", "        \n", "        print(\"\\n=== Comparison ===\")\n", "        print(comparison[\"comparison\"])\n", "    \n", "    # Generate an overall analysis of the fusion retrieval performance\n", "    overall_analysis = generate_overall_analysis(results)\n", "    \n", "    # Return the results and overall analysis\n", "    return {\n", "        \"results\": results,\n", "        \"overall_analysis\": overall_analysis\n", "    }"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def generate_overall_analysis(results):\n", "    \"\"\"\n", "    Generate an overall analysis of fusion retrieval.\n", "    \n", "    Args:\n", "        results (List[Dict]): Results from evaluating queries\n", "        \n", "    Returns:\n", "        str: Overall analysis\n", "    \"\"\"\n", "    # System prompt to guide the evaluation process\n", "    system_prompt = \"\"\"You are an expert at evaluating information retrieval systems. \n", "    Based on multiple test queries, provide an overall analysis comparing three retrieval approaches:\n", "    1. Vector-based retrieval (semantic similarity)\n", "    2. BM25 keyword retrieval (keyword matching)\n", "    3. Fusion retrieval (combination of both)\n", "\n", "    Focus on:\n", "    1. Types of queries where each approach performs best\n", "    2. Overall strengths and weaknesses of each approach\n", "    3. How fusion retrieval balances the trade-offs\n", "    4. Recommendations for when to use each approach\"\"\"\n", "\n", "    # Create a summary of evaluations for each query\n", "    evaluations_summary = \"\"\n", "    for i, result in enumerate(results):\n", "        evaluations_summary += f\"Query {i+1}: {result['query']}\\n\"\n", "        evaluations_summary += f\"Comparison Summary: {result['comparison'][:200]}...\\n\\n\"\n", "\n", "    # User prompt containing the evaluations summary\n", "    user_prompt = f\"\"\"Based on the following evaluations of different retrieval methods across {len(results)} queries, \n", "    provide an overall analysis comparing these three approaches:\n", "\n", "    {evaluations_summary}\n", "\n", "    Please provide a comprehensive analysis of vector-based, BM25, and fusion retrieval approaches,\n", "    highlighting when and why fusion retrieval provides advantages over the individual methods.\"\"\"\n", "\n", "    # Generate the overall analysis using meta-llama/Llama-3.3-70B-Instruct\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    # Return the generated analysis content\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating Fusion Retrieval"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== EVALUATING FUSION RETRIEVAL ===\n", "\n", "Extracting text from data/AI_Information.pdf...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 items to vector store\n", "Created BM25 index with 42 documents\n", "\n", "\n", "=== Evaluating Query 1/1 ===\n", "Query: What are the main applications of transformer models in natural language processing?\n", "\n", "=== Comparing retrieval methods for query: What are the main applications of transformer models in natural language processing? ===\n", "\n", "\n", "Running vector-only RAG...\n", "\n", "Running BM25-only RAG...\n", "\n", "Running fusion RAG...\n", "Performing fusion retrieval for query: What are the main applications of transformer models in natural language processing?\n", "Retrieved 5 documents with fusion retrieval\n", "\n", "Comparing responses...\n", "\n", "=== Vector-based Response ===\n", "The provided context does not mention transformer models specifically. However, it does mention Natural Language Processing (NLP) as a branch of AI that focuses on enabling computers to understand, interpret, and generate human language. NLP techniques are used in chatbots, machine translation, text summarization, and sentiment analysis.\n", "\n", "Transformer models are a type of neural network architecture that is particularly effective for NLP tasks, such as machine translation, text generation, and text classification. They are not explicitly mentioned in the provided context.\n", "\n", "If you're looking for information on transformer models, I can provide general information on this topic. However, please note that the context provided does not specifically address transformer models.\n", "\n", "=== BM25 Response ===\n", "The provided context does not mention transformer models or their applications in natural language processing. The context covers various topics such as deep learning, convolutional neural networks, recurrent neural networks, natural language processing, and machine learning, but it does not specifically discuss transformer models.\n", "\n", "If you're looking for information on transformer models, I can provide general information on this topic. Transformer models are a type of neural network architecture that have gained popularity in natural language processing tasks such as machine translation, text generation, and language understanding. They are particularly effective in handling long-range dependencies in sequential data and have been widely adopted in many NLP applications. However, this information is not present in the provided context.\n", "\n", "=== Fusion Response ===\n", "The provided context does not explicitly mention the main applications of transformer models in natural language processing. However, it does mention that Generative Adversarial Networks (GANs) and transformers are examples of generative AI models that can create original content, including images, text, and music.\n", "\n", "Based on general knowledge, transformer models are widely used in natural language processing (NLP) for tasks such as:\n", "\n", "1. Machine translation\n", "2. Text generation\n", "3. Sentiment analysis\n", "4. Text classification\n", "5. Language modeling\n", "\n", "These models have achieved state-of-the-art results in many NLP tasks and have become a popular choice for many applications.\n", "\n", "If you're looking for more specific information on the applications of transformer models in NLP, I can try to provide more general information or point you in the direction of more resources.\n", "\n", "=== Comparison ===\n", "**Comparison of Vector-based, BM25 Keyword, and Fusion Retrieval Approaches**\n", "\n", "For the given query, \"What are the main applications of transformer models in natural language processing?\", we can evaluate the responses based on relevance, factual correctness, comprehensiveness, and clarity/coherence.\n", "\n", "**Relevance:**\n", "\n", "* Vector-based response: 6/10 (The response is relevant to the query, but it does not directly answer the question. It provides general information about NLP and mentions transformer models, but does not explicitly state their main applications.)\n", "* BM25 keyword response: 5/10 (The response is not directly relevant to the query, as it does not mention transformer models or their applications in NLP.)\n", "* Fusion response: 9/10 (The response directly answers the question and provides a comprehensive list of transformer models' main applications in NLP.)\n", "\n", "**Factual Correctness:**\n", "\n", "* Vector-based response: 8/10 (The response is generally correct, but it does not explicitly mention the main applications of transformer models in NLP.)\n", "* BM25 keyword response: 8/10 (The response is generally correct, but it does not mention transformer models or their applications in NLP.)\n", "* Fusion response: 9/10 (The response is factually correct and provides a comprehensive list of transformer models' main applications in NLP.)\n", "\n", "**Comprehensiveness:**\n", "\n", "* Vector-based response: 6/10 (The response provides general information about NLP, but does not explicitly state the main applications of transformer models.)\n", "* BM25 keyword response: 4/10 (The response does not provide any information about transformer models or their applications in NLP.)\n", "* Fusion response: 9/10 (The response provides a comprehensive list of transformer models' main applications in NLP.)\n", "\n", "**Clarity and Coherence:**\n", "\n", "* Vector-based response: 7/10 (The response is clear, but it does not explicitly state the main applications of transformer models.)\n", "* BM25 keyword response: 6/10 (The response is clear, but it does not mention transformer models or their applications in NLP.)\n", "* Fusion response: 9/10 (The response is clear, concise, and well-organized, making it easy to understand the main applications of transformer models in NLP.)\n", "\n", "**Overall Performance:**\n", "\n", "* Vector-based response: 6.5/10\n", "* BM25 keyword response: 5.5/10\n", "* Fusion response: 8.5/10\n", "\n", "Based on the evaluation, the Fusion retrieval approach performed best for this query. The Fusion response provided a comprehensive list of transformer models' main applications in NLP, was factually correct, and was clear and concise. The Vector-based response was relevant but did not explicitly state the main applications of transformer models, while the BM25 keyword response was not directly relevant to the query.\n", "\n", "\n", "=== OVERALL ANALYSIS ===\n", "\n", "**Overall Analysis: Vector-based, BM25, and Fusion Retrieval Approaches**\n", "\n", "In this analysis, we will evaluate the performance of three retrieval approaches: Vector-based, BM25 Keyword, and Fusion Retrieval. We will examine the strengths and weaknesses of each approach, their performance on specific query types, and how fusion retrieval balances the trade-offs.\n", "\n", "**Query 1: What are the main applications of transformer models in natural language processing?**\n", "\n", "For this query, we can evaluate the performance of the three approaches as follows:\n", "\n", "1. **Vector-based Retrieval (Semantic Similarity)**: This approach is suitable for queries that require understanding the semantic meaning of the query and the documents. In this case, the query is asking about the main applications of transformer models, which implies a need for semantic understanding. The vector-based approach is likely to perform well, as it can capture the nuances of the query and the documents.\n", "\n", "Performance: 8/10\n", "\n", "2. **BM25 Keyword Retrieval (Keyword Matching)**: This approach is suitable for queries that require exact keyword matching. In this case, the query is asking about the main applications of transformer models, which implies a need for exact keyword matching. However, the query is also asking about the main applications, which may require a more nuanced understanding of the documents.\n", "\n", "Performance: 6/10\n", "\n", "3. **Fusion Retrieval (Combination of Both)**: This approach combines the strengths of both vector-based and BM25 keyword retrieval. By using a combination of both approaches, fusion retrieval can capture both the semantic meaning of the query and the exact keyword matching.\n", "\n", "Performance: 9/10\n", "\n", "**Overall Strengths and Weaknesses of Each Approach**\n", "\n", "1. **Vector-based Retrieval (Semantic Similarity)**:\n", "\t* Strengths: Can capture nuances of the query and documents, suitable for queries that require semantic understanding.\n", "\t* Weaknesses: May not perform well for queries that require exact keyword matching.\n", "2. **BM25 Keyword Retrieval (Keyword Matching)**:\n", "\t* Strengths: Can perform well for queries that require exact keyword matching.\n", "\t* Weaknesses: May not capture nuances of the query and documents, suitable for queries that require semantic understanding.\n", "3. **Fusion Retrieval (Combination of Both)**:\n", "\t* Strengths: Can capture both the semantic meaning of the query and the exact keyword matching, suitable for a wide range of queries.\n", "\t* Weaknesses: May require more computational resources and complex implementation.\n", "\n", "**How Fusion Retrieval Balances the Trade-Offs**\n", "\n", "Fusion retrieval balances the trade-offs between vector-based and BM25 keyword retrieval by combining the strengths of both approaches. By using a combination of both, fusion retrieval can capture both the semantic meaning of the query and the exact keyword matching, resulting in a more comprehensive search result.\n", "\n", "**Recommendations for When to Use Each Approach**\n", "\n", "1. **Vector-based Retrieval (Semantic Similarity)**: Use for queries that require semantic understanding, such as questions that ask about the meaning or context of a term.\n", "2. **BM25 Keyword Retrieval (Keyword Matching)**: Use for queries that require exact keyword matching, such as searches for specific terms or phrases.\n", "3. **Fusion Retrieval (Combination of Both)**: Use for queries that require a balance between semantic understanding and exact keyword matching, such as searches for terms or phrases with nuanced meanings.\n", "\n", "In conclusion, fusion retrieval provides advantages over individual methods by combining the strengths of both vector-based and BM25 keyword retrieval. By using a combination of both approaches, fusion retrieval can capture both the semantic meaning of the query and the exact keyword matching, resulting in a more comprehensive search result.\n"]}], "source": ["# Path to PDF document\n", "# Path to PDF document containing AI information for knowledge retrieval testing\n", "pdf_path = \"data/AI_Information.pdf\"\n", "\n", "# Define a single AI-related test query\n", "test_queries = [\n", "    \"What are the main applications of transformer models in natural language processing?\"  # AI-specific query\n", "]\n", "\n", "# Optional reference answer\n", "reference_answers = [\n", "    \"Transformer models have revolutionized natural language processing with applications including machine translation, text summarization, question answering, sentiment analysis, and text generation. They excel at capturing long-range dependencies in text and have become the foundation for models like BERT, GPT, and T5.\",\n", "]\n", "\n", "# Set parameters\n", "k = 5  # Number of documents to retrieve\n", "alpha = 0.5  # Weight for vector scores (0.5 means equal weight between vector and BM25)\n", "\n", "# Run evaluation\n", "evaluation_results = evaluate_fusion_retrieval(\n", "    pdf_path=pdf_path,\n", "    test_queries=test_queries,\n", "    reference_answers=reference_answers,\n", "    k=k,\n", "    alpha=alpha\n", ")\n", "\n", "# Print overall analysis\n", "print(\"\\n\\n=== OVERALL ANALYSIS ===\\n\")\n", "print(evaluation_results[\"overall_analysis\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}