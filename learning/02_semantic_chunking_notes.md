# Semantic Chunking RAG - Learning Notes

### **Core Concept**

- Semantic Chunking = Smart text splitting based on content similarity instead of fixed sizes
- Creates variable-length chunks that respect topic boundaries

### **6-Step Pipeline**

1. **Extract**: `extract_text_from_pdf()` - Get text from documents
2. **Split**: `text.split(". ")` - Break into sentences
3. **Embed**: `get_embedding(sentence)` - Create sentence vectors
4. **Analyze**: `cosine_similarity()` - Compare consecutive sentences
5. **Chunk**: `compute_breakpoints()` + `split_into_chunks()` - Smart splitting
6. **Retrieve**: `semantic_search()` - Find relevant chunks

### **Key Code Insights**

- **Breakpoint method**: `compute_breakpoints(similarities, "percentile", 90)`
- **Similarity formula**: `dot(vec1, vec2) / (||vec1|| * ||vec2||)`
- **Smart splitting**: Chunks split where similarity drops significantly
- **Result**: 231 semantic chunks vs fixed-size chunks

### **Simple Breakpoint Methods**

1. **Percentile** (recommended): Split where similarity < 90th percentile
2. **Standard Deviation**: Split where similarity drops > X std devs below mean
3. **IQR**: Split using statistical outlier detection

### **Advantages vs Fixed Chunking**

✅ **Better**: Respects content boundaries, maintains context
❌ **Cost**: More computation (sentence embeddings + similarity calc)

### **Takeaways**

- Semantic chunking improves retrieval quality by preserving topic coherence
- Trade-off: Better accuracy vs increased computational cost
- Start with percentile method (90th percentile threshold)
- Next: explore hybrid approaches, reranking, advanced similarity metrics
