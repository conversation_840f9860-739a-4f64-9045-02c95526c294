# Relevant Segment Extraction (RSE) RAG Diagram

```mermaid
graph TD
    A[Document: PDF] -->|"extract_text_from_pdf()"| B[Raw Text]
    B -->|"chunk_text(800, overlap=0)"| C[Non-overlapping Chunks: 42 chunks]
    C -->|"create_embeddings()"| D[Chunk Embeddings]
    D -->|Store with index metadata| E[(Vector Store)]
    
    F[User Query] -->|"create_embeddings()"| G[Query Embedding]
    G -->|"search(top_k=all)"| E
    E -->|"All chunks with scores"| H[Relevance Scores]
    
    H -->|"score - penalty(0.2)"| I[Chunk Values]
    I -->|"find_best_segments()"| J[Segment Detection Algorithm]
    
    J -->|"Maximum sum subarray"| K[Continuous Segments]
    K -->|"Example: (21,41), (0,20)"| L[Best Segment Ranges]
    
    L -->|"reconstruct_segments()"| M[Reconstructed Text Segments]
    M -->|"format_segments_for_context()"| N[Formatted Context]
    
    F -->|"Format with context"| O[Prompt]
    N -->|"Format with context"| O
    O -->|"generate_response()"| P[Final Answer]
    
    subgraph "RSE Core Algorithm"
        Q[Calculate chunk values: relevance - penalty]
        R[Find continuous segments with max value]
        S[Reconstruct coherent text from segments]
    end
    
    subgraph "Segment Detection Process"
        T[Try all possible start positions]
        U[Try all possible segment lengths]
        V[Calculate segment value as sum of chunk values]
        W[Select segments with value > threshold]
        T --> U --> V --> W
    end
    
    subgraph "Key Parameters"
        X[max_segment_length: 20 chunks]
        Y[total_max_length: 30 chunks]
        Z[min_segment_value: 0.2]
        AA[irrelevant_chunk_penalty: 0.2]
    end
```
