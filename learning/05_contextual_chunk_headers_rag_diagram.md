# Contextual Chunk Headers RAG Diagram

```mermaid
graph TD
    A[Document: PDF] -->|"extract_text_from_pdf()"| B[Raw Text]
    B -->|"chunk_text(1000, 200)"| C[Text Chunks: 42 chunks]
    
    C -->|"generate_chunk_header()"| D[LLM Header Generation]
    D -->|"Generate concise title"| E[Chunk Headers]
    
    C -->|"create_embeddings(text)"| F[Text Embeddings]
    E -->|"create_embeddings(header)"| G[Header Embeddings]
    
    F -->|Store| H[(Vector Store: Text)]
    G -->|Store| I[(Vector Store: Headers)]
    
    J[User Query] -->|"create_embeddings()"| K[Query Vector]
    
    K -->|"cosine_similarity()"| H
    K -->|"cosine_similarity()"| I
    
    H -->|"Text similarity"| L[Text Scores]
    I -->|"Header similarity"| M[Header Scores]
    
    L -->|"Average scores"| N[Combined Similarity]
    M -->|"Average scores"| N
    
    N -->|"Top k chunks"| O[Retrieved Context with Headers]
    
    J -->|"Format with headers"| P[Prompt]
    O -->|"Format with headers"| P
    P -->|"generate_response()"| Q[Final Answer]
    
    subgraph "Header Enhancement"
        R[Chunk Text] -->|LLM| S[Generated Header]
        S -->|Example| T["Building Trust in AI: Addressing Transparency..."]
    end
```
