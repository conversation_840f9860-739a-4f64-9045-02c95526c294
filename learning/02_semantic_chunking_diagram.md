# Semantic Chunking RAG Diagram

```mermaid
graph TD
    A[Document: PDF] -->|"extract_text_from_pdf()"| B[Raw Text]
    B -->|"split('.')"| C[Sentences]
    C -->|"get_embedding()"| D[Sentence Embeddings]
    
    D -->|"cosine_similarity()"| E[Similarity Scores]
    E -->|"compute_breakpoints(percentile=90)"| F[Breakpoints]

    C -->|Combined with| F
    F -->|"split_into_chunks(sentences, breakpoints)"| G[Semantic Chunks]
    G -->|"create_embeddings()"| H[Chunk Embeddings]
    H -->|Store| I[(Vector Store)]
    
    J[User Query] -->|"get_embedding()"| K[Query Vector]
    K -->|"semantic_search(k=5)"| I
    I -->|"Top k chunks"| L[Retrieved Context]
    
    J -->|"Format with context"| M[Prompt]
    L -->|"Format with context"| M
    M -->|"generate_response()"| N[Final Answer]
```

## Key Differences from Simple RAG:

1. **Sentence-Level Processing**: Text is first split into sentences rather than fixed-size chunks
2. **Semantic Analysis**: Each sentence gets embedded and similarity is calculated between consecutive sentences
3. **Intelligent Breakpoints**: Uses statistical methods (percentile, standard deviation, or IQR) to determine optimal chunk boundaries
4. **Content-Aware Chunking**: Chunks are created based on semantic similarity rather than arbitrary size limits

## Breakpoint Methods:
- **Percentile**: Splits where similarity drops below the Xth percentile
- **Standard Deviation**: Splits where similarity drops more than X standard deviations below mean
- **Interquartile Range (IQR)**: Uses Q1 - 1.5*(Q3-Q1) as threshold
