# Context-Enriched RAG Diagram

```mermaid
graph TD
    A[Document: PDF] -->|"extract_text_from_pdf()"| B[Raw Text]
    B -->|"chunk_text(1000, 200)"| C[Text Chunks: 42 chunks]
    C -->|"create_embeddings()"| D[Chunk Embeddings]
    D -->|Store| E[(Vector Store)]

    F[User Query] -->|"create_embeddings()"| G[Query Vector]
    G -->|"cosine_similarity()"| E
    E -->|"Find top match"| H[Best Match Index]

    H -->|"context_size=1"| I[Context Range Calculation]
    I -->|"Retrieve neighbors"| J[Context-Enriched Chunks]
    
    F -->|"Format with context"| K[Prompt]
    J -->|"Format with context"| K
    K -->|"generate_response()"| L[Final Answer]
    
    subgraph "Context Enrichment"
        M[Chunk i-1: Previous Context]
        N[Chunk i: Best Match]
        O[Chunk i+1: Next Context]
        M --> P[Combined Context]
        N --> P
        O --> P
    end
```
