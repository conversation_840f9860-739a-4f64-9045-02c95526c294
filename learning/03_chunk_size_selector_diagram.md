# Chunk Size Selector RAG Diagram

```mermaid
graph TD
    A[Document: PDF] -->|"extract_text_from_pdf()"| B[Raw Text]
    
    B -->|"chunk_text(text, 128, 26)"| C1[Chunks: 128 chars<br/>326 chunks]
    B -->|"chunk_text(text, 256, 51)"| C2[Chunks: 256 chars<br/>164 chunks]
    B -->|"chunk_text(text, 512, 102)"| C3[Chunks: 512 chars<br/>82 chunks]
    
    C1 -->|"create_embeddings()"| D1[Embeddings 128]
    C2 -->|"create_embeddings()"| D2[Embeddings 256]
    C3 -->|"create_embeddings()"| D3[Embeddings 512]
    
    D1 -->|Store| E1[(Vector Store 128)]
    D2 -->|Store| E2[(Vector Store 256)]
    D3 -->|Store| E3[(Vector Store 512)]
    
    F[User Query] -->|"create_embeddings()"| G[Query Vector]
    
    G -->|"retrieve_relevant_chunks(k=5)"| E1
    G -->|"retrieve_relevant_chunks(k=5)"| E2
    G -->|"retrieve_relevant_chunks(k=5)"| E3
    
    E1 -->|"Top 5 chunks"| H1[Context 128]
    E2 -->|"Top 5 chunks"| H2[Context 256]
    E3 -->|"Top 5 chunks"| H3[Context 512]
    
    F -->|"Format with context"| I1[Prompt 128]
    F -->|"Format with context"| I2[Prompt 256]
    F -->|"Format with context"| I3[Prompt 512]
    
    H1 -->|"Format with context"| I1
    H2 -->|"Format with context"| I2
    H3 -->|"Format with context"| I3
    
    I1 -->|"generate_response()"| J1[Response 128]
    I2 -->|"generate_response()"| J2[Response 256]
    I3 -->|"generate_response()"| J3[Response 512]
    
    J1 -->|"evaluate_response()"| K1[Faithfulness: 0.5<br/>Relevancy: 0.5]
    J2 -->|"evaluate_response()"| K2[Faithfulness: 0.5<br/>Relevancy: 0.5]
    J3 -->|"evaluate_response()"| K3[Faithfulness: ?<br/>Relevancy: ?]
    
    K1 --> L[Best Chunk Size Selection]
    K2 --> L
    K3 --> L
```
