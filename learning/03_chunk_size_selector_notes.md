# Chunk Size Selector RAG - Learning Notes

### **Core Concept**

- Chunk Size Selector = Test multiple chunk sizes in parallel to find optimal performance
- Key difference: Evaluation-driven optimization instead of guessing chunk size

### **Key Difference from Simple RAG**

Instead of fixed `chunk_text(1000, 200)`, test multiple sizes:
```python
chunk_sizes = [128, 256, 512]
text_chunks_dict = {size: chunk_text(text, size, size//5) for size in chunk_sizes}
```

### **Evaluation Framework**

```python
faithfulness, relevancy = evaluate_response(query, response, true_answer)
```

- **Faithfulness**: No hallucinations vs ground truth (0.0-1.0)
- **Relevancy**: Addresses user query (0.0-1.0)
- **Results**: 128→0.5/0.5, 256→0.5/0.5, 512→? (compare to select best)

### **Key Insights**

- **Chunk counts**: 128→326, 256→164, 512→82 chunks
- **Trade-off**: Smaller chunks = precise retrieval, Larger chunks = more context
- **Overlap formula**: `size // 5` maintains proportional overlap

### **Takeaways**

- Data-driven beats guessing chunk parameters
- Systematic evaluation reveals optimal size for your specific use case
- Next: adaptive chunking, multi-scale retrieval
