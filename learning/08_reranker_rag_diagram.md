# Reranker RAG Diagram

```mermaid
graph TD
    A[Document: PDF] -->|"extract_text_from_pdf()"| B[Raw Text]
    B -->|"chunk_text(1000, 200)"| C[Text Chunks: 42 chunks]
    C -->|"create_embeddings()"| D[Chunk Embeddings]
    D -->|Store| E[(Vector Store)]
    
    F[User Query] -->|"create_embeddings()"| G[Query Vector]
    G -->|"similarity_search(k=10)"| E
    E -->|"Initial retrieval"| H[Top 10 Candidates]
    
    H -->|"Choose method"| I{Reranking Method}
    
    I -->|"LLM-based"| J[LLM Reranker]
    I -->|"Keyword-based"| K[Keyword Reranker]
    I -->|"None"| L[No Reranking]
    
    J -->|"Score 0-10 per doc"| M[LLM Relevance Scores]
    K -->|"Keyword matching + position"| N[Keyword Relevance Scores]
    L -->|"Use original order"| O[Original Similarity Scores]
    
    M -->|"Sort by score"| P[Reranked Results: Top 3]
    N -->|"Sort by score"| P
    O -->|"Take top 3"| P
    
    P -->|"Combine contexts"| Q[Final Context]
    
    F -->|"Format with context"| R[Prompt]
    Q -->|"Format with context"| R
    R -->|"generate_response()"| S[Final Answer]
    
    subgraph "LLM Reranking Process"
        T[Document] -->|"Rate relevance 0-10"| U[LLM Scorer]
        U -->|"Extract score"| V[Numerical Score]
    end
    
    subgraph "Keyword Reranking Process"
        W[Document] -->|"Extract keywords"| X[Keyword Matcher]
        X -->|"Position + frequency"| Y[Keyword Score]
        Y -->|"Combine with similarity"| Z[Final Score]
    end
```
