# Document Augmentation RAG Diagram

```mermaid
graph TD
    A[Document: PDF] -->|"extract_text_from_pdf()"| B[Raw Text]
    B -->|"chunk_text(1000, 200)"| C[Text Chunks: 42 chunks]
    
    C -->|"generate_questions(num=3)"| D[LLM Question Generation]
    D -->|"Create relevant questions"| E[Generated Questions: 126 questions]
    
    C -->|"create_embeddings()"| F[Chunk Embeddings]
    E -->|"create_embeddings()"| G[Question Embeddings]
    
    F -->|Store with metadata| H[(Vector Store)]
    G -->|Store with metadata| H
    
    H -->|Contains| I[165 Total Items:<br/>42 chunks + 126 questions]
    
    J[User Query] -->|"create_embeddings()"| K[Query Vector]
    K -->|"similarity_search(k=5)"| H
    
    H -->|"Mixed results"| L[Retrieved Items:<br/>Questions + Chunks]
    
    L -->|"prepare_context()"| M[Context Preparation]
    M -->|"Combine unique chunks"| N[Unified Context]
    
    J -->|"Format with context"| O[Prompt]
    N -->|"Format with context"| O
    O -->|"generate_response()"| P[Final Answer]
    
    subgraph "Question Generation Process"
        Q[Text Chunk] -->|LLM| R[Generated Questions]
        R -->|Example| S["What is the main goal of XAI?<br/>Why is explainability important?<br/>What techniques does XAI use?"]
    end
    
    subgraph "Augmented Vector Store"
        T[Chunk + metadata: type=chunk]
        U[Question + metadata: type=question, chunk_index, original_chunk]
    end
```
