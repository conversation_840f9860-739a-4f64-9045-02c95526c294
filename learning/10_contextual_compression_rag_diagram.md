# Contextual Compression RAG Diagram

```mermaid
graph TD
    A[Document: PDF] -->|"extract_text_from_pdf()"| B[Raw Text]
    B -->|"chunk_text(1000, 200)"| C[Text Chunks: 42 chunks]
    C -->|"create_embeddings()"| D[Chunk Embeddings]
    D -->|Store| E[(Vector Store)]
    
    F[User Query] -->|"create_embeddings()"| G[Query Embedding]
    G -->|"similarity_search(k=10)"| E
    E -->|"Top 10 chunks"| H[Retrieved Chunks]
    
    H -->|"Choose compression type"| I{Compression Method}
    
    I -->|"Selective"| J[Selective Compression]
    I -->|"Summary"| K[Summary Compression]
    I -->|"Extraction"| L[Extraction Compression]
    
    J -->|"LLM filters relevant sentences"| M[Compressed Chunks: Selective]
    K -->|"LLM creates concise summaries"| N[Compressed Chunks: Summary]
    L -->|"LLM extracts exact sentences"| O[Compressed Chunks: Extraction]
    
    M -->|"39.93% compression"| P[Final Context]
    N -->|"63.87% compression"| P
    O -->|"54.41% compression"| P
    
    F -->|"Format with context"| Q[Prompt]
    P -->|"Format with context"| Q
    Q -->|"generate_response()"| R[Final Answer]
    
    subgraph "Compression Types"
        S[Selective: Keep only relevant sentences, preserve exact wording]
        T[Summary: Create concise summaries focused on query]
        U[Extraction: Extract exact relevant sentences verbatim]
    end
    
    subgraph "LLM Compression Process"
        V[Original Chunk] -->|"compress_chunk()"| W[LLM Analysis]
        W -->|"Filter by relevance"| X[Compressed Output]
        X -->|"Calculate ratio"| Y[Compression Metrics]
    end
    
    subgraph "Benefits"
        Z[Reduced noise and irrelevant information]
        AA[Maximized useful signal in context window]
        BB[Improved response quality and focus]
    end
```
