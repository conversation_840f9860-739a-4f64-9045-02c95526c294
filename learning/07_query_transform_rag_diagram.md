# Query Transform RAG Diagram

```mermaid
graph TD
    A[Document: PDF] -->|"extract_text_from_pdf()"| B[Raw Text]
    B -->|"chunk_text(1000, 200)"| C[Text Chunks: 42 chunks]
    C -->|"create_embeddings()"| D[Chunk Embeddings]
    D -->|Store| E[(Vector Store)]
    
    F[User Query] -->|"Choose transformation"| G{Query Transformation}
    
    G -->|"None"| H[Original Query]
    G -->|"Rewrite"| I[Query Rewriting]
    G -->|"Step-back"| J[Step-back Prompting]
    G -->|"Decompose"| K[Sub-query Decomposition]
    
    I -->|"Make more specific"| L[Rewritten Query]
    J -->|"Make more general"| M[Step-back Query]
    K -->|"Break into parts"| N[Multiple Sub-queries]
    
    H -->|"create_embeddings()"| O[Query Embedding]
    L -->|"create_embeddings()"| P[Rewritten Embedding]
    M -->|"create_embeddings()"| Q[Step-back Embedding]
    N -->|"create_embeddings()"| R[Sub-query Embeddings]
    
    O -->|"similarity_search(k=3)"| E
    P -->|"similarity_search(k=3)"| E
    Q -->|"similarity_search(k=3)"| E
    R -->|"similarity_search(k=2 each)"| E
    
    E -->|"Retrieved chunks"| S[Search Results]
    R -->|"Combine & deduplicate"| S
    
    S -->|"Combine contexts"| T[Final Context]
    
    F -->|"Format with context"| U[Prompt]
    T -->|"Format with context"| U
    U -->|"generate_response()"| V[Final Answer]
    
    subgraph "Query Transformation Types"
        W[Rewrite: More specific & detailed]
        X[Step-back: Broader & general]
        Y[Decompose: Multiple focused queries]
    end
    
    subgraph "Example Transformations"
        Z["Original: What is Explainable AI?"]
        AA["Rewritten: What is XAI in ML context with applications?"]
        BB["Step-back: Background on XAI in AI field"]
        CC["Decomposed: 1. Definition 2. Goals 3. Challenges 4. Importance"]
    end
```
