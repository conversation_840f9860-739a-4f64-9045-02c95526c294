import os
import numpy as np
import re
from openai import OpenAI

def read_markdown_file(markdown_path):
    """
    Reads and processes text from a Markdown file.

    Args:
    markdown_path (str): Path to the Markdown file.

    Returns:
    str: Processed text from the Markdown file.
    """
    try:
        # Open and read the Markdown file
        with open(markdown_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Basic preprocessing: remove excessive whitespace and normalize line breaks
        # Remove markdown headers symbols for cleaner text processing
        content = re.sub(r'^#{1,6}\s*', '', content, flags=re.MULTILINE)
        
        # Remove markdown links but keep the text
        content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)
        
        # Remove markdown emphasis markers
        content = re.sub(r'[*_]{1,2}([^*_]+)[*_]{1,2}', r'\1', content)
        
        # Normalize whitespace
        content = re.sub(r'\n\s*\n', '\n\n', content)  # Normalize paragraph breaks
        content = re.sub(r'[ \t]+', ' ', content)  # Normalize spaces and tabs
        
        return content.strip()
        
    except FileNotFoundError:
        raise FileNotFoundError(f"Markdown file not found: {markdown_path}")
    except Exception as e:
        raise Exception(f"Error reading markdown file: {str(e)}")

# Define the path to the Markdown file
markdown_path = "data/aig-doc.md"

# Read and process text from the Markdown file
extracted_text = read_markdown_file(markdown_path)

# Print the first 500 characters of the extracted text
print(f"Successfully loaded Markdown file with {len(extracted_text)} characters")
print("\nFirst 500 characters:")
print(extracted_text[:500])

# Initialize the OpenAI client with the base URL and API key
client = OpenAI(
    base_url="https://api.studio.nebius.com/v1/",
    api_key=os.getenv("OPENAI_API_KEY")  # Retrieve the API key from environment variables
)

def get_embedding(text, model="BAAI/bge-en-icl"):
    """
    Creates an embedding for the given text using OpenAI.

    Args:
    text (str): Input text.
    model (str): Embedding model name.

    Returns:
    np.ndarray: The embedding vector.
    """
    response = client.embeddings.create(model=model, input=text)
    return np.array(response.data[0].embedding)

def split_markdown_into_sentences(text):
    """
    Splits Markdown text into meaningful sentences, handling various punctuation and structure.
    
    Args:
    text (str): Input text from Markdown file.
    
    Returns:
    List[str]: List of sentences.
    """
    # Split on sentence-ending punctuation followed by whitespace
    sentences = re.split(r'[.!?]+\s+', text)
    
    # Filter out very short sentences and empty strings
    sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
    
    # Remove sentences that are just punctuation or special characters
    sentences = [s for s in sentences if re.search(r'[a-zA-Z]', s)]
    
    return sentences

# Split text into sentences using improved method for Markdown
sentences = split_markdown_into_sentences(extracted_text)



print(sentences[:10])
print(len(sentences))

# Generate embeddings for each sentence
embeddings = [get_embedding(sentence) for sentence in sentences]

print(f"Generated {len(embeddings)} sentence embeddings from {len(sentences)} sentences.")

def cosine_similarity(vec1, vec2):
    """
    Computes cosine similarity between two vectors.

    Args:
    vec1 (np.ndarray): First vector.
    vec2 (np.ndarray): Second vector.

    Returns:
    float: Cosine similarity.
    """
    return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))

# Compute similarity between consecutive sentences
similarities = [cosine_similarity(embeddings[i], embeddings[i + 1]) for i in range(len(embeddings) - 1)]

def compute_breakpoints(similarities, method="percentile", threshold=90):
    """
    Computes chunking breakpoints based on similarity drops.

    Args:
    similarities (List[float]): List of similarity scores between sentences.
    method (str): 'percentile', 'standard_deviation', or 'interquartile'.
    threshold (float): Threshold value (percentile for 'percentile', std devs for 'standard_deviation').

    Returns:
    List[int]: Indices where chunk splits should occur.
    """
    # Determine the threshold value based on the selected method
    if method == "percentile":
        # Calculate the Xth percentile of the similarity scores
        threshold_value = np.percentile(similarities, threshold)
    elif method == "standard_deviation":
        # Calculate the mean and standard deviation of the similarity scores
        mean = np.mean(similarities)
        std_dev = np.std(similarities)
        # Set the threshold value to mean minus X standard deviations
        threshold_value = mean - (threshold * std_dev)
    elif method == "interquartile":
        # Calculate the first and third quartiles (Q1 and Q3)
        q1, q3 = np.percentile(similarities, [25, 75])
        # Set the threshold value using the IQR rule for outliers
        threshold_value = q1 - 1.5 * (q3 - q1)
    else:
        # Raise an error if an invalid method is provided
        raise ValueError("Invalid method. Choose 'percentile', 'standard_deviation', or 'interquartile'.")

    # Identify indices where similarity drops below the threshold value
    return [i for i, sim in enumerate(similarities) if sim < threshold_value]

# Compute breakpoints using the percentile method with a threshold of 90
breakpoints = compute_breakpoints(similarities, method="percentile", threshold=90)

print(breakpoints)

def split_into_chunks(sentences, breakpoints):
    """
    Splits sentences into semantic chunks.

    Args:
    sentences (List[str]): List of sentences.
    breakpoints (List[int]): Indices where chunking should occur.

    Returns:
    List[str]: List of text chunks.
    """
    chunks = []  # Initialize an empty list to store the chunks
    start = 0  # Initialize the start index

    # Iterate through each breakpoint to create chunks
    for bp in breakpoints:
        # Append the chunk of sentences from start to the current breakpoint
        chunks.append(". ".join(sentences[start:bp + 1]) + ".")
        start = bp + 1  # Update the start index to the next sentence after the breakpoint

    # Append the remaining sentences as the last chunk
    chunks.append(". ".join(sentences[start:]))
    return chunks  # Return the list of chunks

# Create chunks using the split_into_chunks function
text_chunks = split_into_chunks(sentences, breakpoints)

# Print the number of chunks created
print(f"Number of semantic chunks: {len(text_chunks)}")

# Print the first chunk to verify the result
print("\nFirst text chunk:")
print(text_chunks[0])


def create_embeddings(text_chunks):
    """
    Creates embeddings for each text chunk.

    Args:
    text_chunks (List[str]): List of text chunks.

    Returns:
    List[np.ndarray]: List of embedding vectors.
    """
    # Generate embeddings for each text chunk using the get_embedding function
    return [get_embedding(chunk) for chunk in text_chunks]

# Create chunk embeddings using the create_embeddings function
chunk_embeddings = create_embeddings(text_chunks)

def semantic_search(query, text_chunks, chunk_embeddings, k=5):
    """
    Finds the most relevant text chunks for a query.

    Args:
    query (str): Search query.
    text_chunks (List[str]): List of text chunks.
    chunk_embeddings (List[np.ndarray]): List of chunk embeddings.
    k (int): Number of top results to return.

    Returns:
    List[str]: Top-k relevant chunks.
    """
    # Generate an embedding for the query
    query_embedding = get_embedding(query)
    
    # Calculate cosine similarity between the query embedding and each chunk embedding
    similarities = [cosine_similarity(query_embedding, emb) for emb in chunk_embeddings]
    
    # Get the indices of the top-k most similar chunks
    top_indices = np.argsort(similarities)[-k:][::-1]
    
    # Return the top-k most relevant text chunks
    return [text_chunks[i] for i in top_indices]

# Define a test query relevant to the AIGuardian documentation

# query = "Is there a instance where we can test out the [Sentinel]"
# query = "How to get starting with Litmus"
# query = ""


# Get top 3 relevant chunks for better context
top_chunks = semantic_search(query, text_chunks, chunk_embeddings, k=5)

# Print the query
print(f"Query: {query}")
print("\n" + "="*60)

# Print the top 3 most relevant text chunks
for i, chunk in enumerate(top_chunks):
    print(f"\nContext {i+1}:")
    print("-" * 40)
    print(chunk)
    print("="*60)

# Define the system prompt for the AI assistant
system_prompt = "You are an AI assistant that strictly answers based on the given context. If the answer cannot be derived directly from the provided context, respond with: 'I do not have enough information to answer that.'"

def generate_response(system_prompt, user_message, model="meta-llama/Llama-3.3-70B-Instruct"):
    """
    Generates a response from the AI model based on the system prompt and user message.

    Args:
    system_prompt (str): The system prompt to guide the AI's behavior.
    user_message (str): The user's message or query.
    model (str): The model to be used for generating the response. Default is "meta-llama/Llama-2-7B-chat-hf".

    Returns:
    dict: The response from the AI model.
    """
    response = client.chat.completions.create(
        model=model,
        temperature=0,
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
    )
    return response

# Create the user prompt based on the top chunks
user_prompt = "\n".join([f"Context {i + 1}:\n{chunk}\n=====================================\n" for i, chunk in enumerate(top_chunks)])
user_prompt = f"{user_prompt}\nQuestion: {query}"

# Generate AI response
ai_response = generate_response(system_prompt, user_prompt)

print(ai_response.choices[0].message.content)

# Define the system prompt for the evaluation system
evaluate_system_prompt = "You are an intelligent evaluation system tasked with assessing AI assistant responses. Evaluate the response based on: 1) Accuracy relative to the provided context, 2) Completeness of the answer, 3) Clarity and organization. Provide a score from 0-1 and explain your reasoning."

# Create the evaluation prompt
evaluation_prompt = f"""User Query: {query}

AI Response:
{ai_response.choices[0].message.content}

Context provided to AI:
{''.join([f'Context {i+1}: {chunk}\n\n' for i, chunk in enumerate(top_chunks)])}

Please evaluate this response based on accuracy, completeness, and clarity."""

# Generate the evaluation response
evaluation_response = generate_response(evaluate_system_prompt, evaluation_prompt)

# Print the evaluation response
print("=== EVALUATION RESULTS ===")
print(evaluation_response.choices[0].message.content)

