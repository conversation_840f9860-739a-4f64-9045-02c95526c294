{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["# Hierarchical Indices for RAG\n", "\n", "In this notebook, I implement a hierarchical indexing approach for RAG systems. This technique improves retrieval by using a two-tier search method: first identifying relevant document sections through summaries, then retrieving specific details from those sections.\n", "\n", "Traditional RAG approaches treat all text chunks equally, which can lead to:\n", "\n", "- Lost context when chunks are too small\n", "- Irrelevant results when the document collection is large\n", "- Inefficient searches across the entire corpus\n", "\n", "Hierarchical retrieval solves these problems by:\n", "\n", "- Creating concise summaries for larger document sections\n", "- First searching these summaries to identify relevant sections\n", "- Then retrieving detailed information only from those sections\n", "- Maintaining context while preserving specific details"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "We begin by importing necessary libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import json\n", "import fitz\n", "from openai import OpenAI\n", "import re\n", "import pickle"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the OpenAI API Client\n", "We initialize the OpenAI client to generate embeddings and responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the OpenAI client with the base URL and API key\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",\n", "    api_key=os.getenv(\"OPENAI_API_KEY\")  # Retrieve the API key from environment variables\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Processing Functions"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def extract_text_from_pdf(pdf_path):\n", "    \"\"\"\n", "    Extract text content from a PDF file with page separation.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        \n", "    Returns:\n", "        List[Dict]: List of pages with text content and metadata\n", "    \"\"\"\n", "    print(f\"Extracting text from {pdf_path}...\")  # Print the path of the PDF being processed\n", "    pdf = fitz.open(pdf_path)  # Open the PDF file using PyMuPDF\n", "    pages = []  # Initialize an empty list to store the pages with text content\n", "    \n", "    # Iterate over each page in the PDF\n", "    for page_num in range(len(pdf)):\n", "        page = pdf[page_num]  # Get the current page\n", "        text = page.get_text()  # Extract text from the current page\n", "        \n", "        # Skip pages with very little text (less than 50 characters)\n", "        if len(text.strip()) > 50:\n", "            # Append the page text and metadata to the list\n", "            pages.append({\n", "                \"text\": text,\n", "                \"metadata\": {\n", "                    \"source\": pdf_path,  # Source file path\n", "                    \"page\": page_num + 1  # Page number (1-based index)\n", "                }\n", "            })\n", "    \n", "    print(f\"Extracted {len(pages)} pages with content\")  # Print the number of pages extracted\n", "    return pages  # Return the list of pages with text content and metadata"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def chunk_text(text, metadata, chunk_size=1000, overlap=200):\n", "    \"\"\"\n", "    Split text into overlapping chunks while preserving metadata.\n", "    \n", "    Args:\n", "        text (str): Input text to chunk\n", "        metadata (Dict): Metadata to preserve\n", "        chunk_size (int): Size of each chunk in characters\n", "        overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        List[Dict]: List of text chunks with metadata\n", "    \"\"\"\n", "    chunks = []  # Initialize an empty list to store the chunks\n", "    \n", "    # Iterate over the text with the specified chunk size and overlap\n", "    for i in range(0, len(text), chunk_size - overlap):\n", "        chunk_text = text[i:i + chunk_size]  # Extract the chunk of text\n", "        \n", "        # Skip very small chunks (less than 50 characters)\n", "        if chunk_text and len(chunk_text.strip()) > 50:\n", "            # Create a copy of metadata and add chunk-specific info\n", "            chunk_metadata = metadata.copy()\n", "            chunk_metadata.update({\n", "                \"chunk_index\": len(chunks),  # Index of the chunk\n", "                \"start_char\": i,  # Start character index of the chunk\n", "                \"end_char\": i + len(chunk_text),  # End character index of the chunk\n", "                \"is_summary\": False  # Flag indicating this is not a summary\n", "            })\n", "            \n", "            # Append the chunk with its metadata to the list\n", "            chunks.append({\n", "                \"text\": chunk_text,\n", "                \"metadata\": chunk_metadata\n", "            })\n", "    \n", "    return chunks  # Return the list of chunks with metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple Vector Store Implementation"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class SimpleVectorStore:\n", "    \"\"\"\n", "    A simple vector store implementation using NumPy.\n", "    \"\"\"\n", "    def __init__(self):\n", "        self.vectors = []  # List to store vector embeddings\n", "        self.texts = []  # List to store text content\n", "        self.metadata = []  # List to store metadata\n", "    \n", "    def add_item(self, text, embedding, metadata=None):\n", "        \"\"\"\n", "        Add an item to the vector store.\n", "        \n", "        Args:\n", "            text (str): Text content\n", "            embedding (List[float]): Vector embedding\n", "            metadata (Dict, optional): Additional metadata\n", "        \"\"\"\n", "        self.vectors.append(np.array(embedding))  # Append the embedding as a numpy array\n", "        self.texts.append(text)  # Append the text content\n", "        self.metadata.append(metadata or {})  # Append the metadata or an empty dict if None\n", "    \n", "    def similarity_search(self, query_embedding, k=5, filter_func=None):\n", "        \"\"\"\n", "        Find the most similar items to a query embedding.\n", "        \n", "        Args:\n", "            query_embedding (List[float]): Query embedding vector\n", "            k (int): Number of results to return\n", "            filter_func (callable, optional): Function to filter results\n", "            \n", "        Returns:\n", "            List[Dict]: Top k most similar items\n", "        \"\"\"\n", "        if not self.vectors:\n", "            return []  # Return an empty list if there are no vectors\n", "        \n", "        # Convert query embedding to numpy array\n", "        query_vector = np.array(query_embedding)\n", "        \n", "        # Calculate similarities using cosine similarity\n", "        similarities = []\n", "        for i, vector in enumerate(self.vectors):\n", "            # Skip if doesn't pass the filter\n", "            if filter_func and not filter_func(self.metadata[i]):\n", "                continue\n", "                \n", "            # Calculate cosine similarity\n", "            similarity = np.dot(query_vector, vector) / (np.linalg.norm(query_vector) * np.linalg.norm(vector))\n", "            similarities.append((i, similarity))  # Append index and similarity score\n", "        \n", "        # Sort by similarity (descending)\n", "        similarities.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        # Return top k results\n", "        results = []\n", "        for i in range(min(k, len(similarities))):\n", "            idx, score = similarities[i]\n", "            results.append({\n", "                \"text\": self.texts[idx],  # Add the text content\n", "                \"metadata\": self.metadata[idx],  # Add the metadata\n", "                \"similarity\": float(score)  # Add the similarity score\n", "            })\n", "        \n", "        return results  # Return the list of top k results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Embeddings"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def create_embeddings(texts, model=\"BAAI/bge-en-icl\"):\n", "    \"\"\"\n", "    Create embeddings for the given texts.\n", "    \n", "    Args:\n", "        texts (List[str]): Input texts\n", "        model (str): Embedding model name\n", "        \n", "    Returns:\n", "        List[List[float]]: Embedding vectors\n", "    \"\"\"\n", "    # Handle empty input\n", "    if not texts:\n", "        return []\n", "        \n", "    # Process in batches if needed (OpenAI API limits)\n", "    batch_size = 100\n", "    all_embeddings = []\n", "    \n", "    # Iterate over the input texts in batches\n", "    for i in range(0, len(texts), batch_size):\n", "        batch = texts[i:i + batch_size]  # Get the current batch of texts\n", "        \n", "        # Create embeddings for the current batch\n", "        response = client.embeddings.create(\n", "            model=model,\n", "            input=batch\n", "        )\n", "        \n", "        # Extract embeddings from the response\n", "        batch_embeddings = [item.embedding for item in response.data]\n", "        all_embeddings.extend(batch_embeddings)  # Add the batch embeddings to the list\n", "    \n", "    return all_embeddings  # Return all embeddings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summarization Function"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def generate_page_summary(page_text):\n", "    \"\"\"\n", "    Generate a concise summary of a page.\n", "    \n", "    Args:\n", "        page_text (str): Text content of the page\n", "        \n", "    Returns:\n", "        str: Generated summary\n", "    \"\"\"\n", "    # Define the system prompt to instruct the summarization model\n", "    system_prompt = \"\"\"You are an expert summarization system.\n", "    Create a detailed summary of the provided text. \n", "    Focus on capturing the main topics, key information, and important facts.\n", "    Your summary should be comprehensive enough to understand what the page contains\n", "    but more concise than the original.\"\"\"\n", "\n", "    # Truncate input text if it exceeds the maximum token limit\n", "    max_tokens = 6000\n", "    truncated_text = page_text[:max_tokens] if len(page_text) > max_tokens else page_text\n", "\n", "    # Make a request to the OpenAI API to generate the summary\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",  # Specify the model to use\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},  # System message to guide the assistant\n", "            {\"role\": \"user\", \"content\": f\"Please summarize this text:\\n\\n{truncated_text}\"}  # User message with the text to summarize\n", "        ],\n", "        temperature=0.3  # Set the temperature for response generation\n", "    )\n", "    \n", "    # Return the generated summary content\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hierarchical Document Processing"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def process_document_hierarchically(pdf_path, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Process a document into hierarchical indices.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        chunk_size (int): Size of each detailed chunk\n", "        chunk_overlap (int): Overlap between chunks\n", "        \n", "    Returns:\n", "        Tuple[SimpleVectorStore, SimpleVectorStore]: Summary and detailed vector stores\n", "    \"\"\"\n", "    # Extract pages from PDF\n", "    pages = extract_text_from_pdf(pdf_path)\n", "    \n", "    # Create summaries for each page\n", "    print(\"Generating page summaries...\")\n", "    summaries = []\n", "    for i, page in enumerate(pages):\n", "        print(f\"Summarizing page {i+1}/{len(pages)}...\")\n", "        summary_text = generate_page_summary(page[\"text\"])\n", "        \n", "        # Create summary metadata\n", "        summary_metadata = page[\"metadata\"].copy()\n", "        summary_metadata.update({\"is_summary\": True})\n", "        \n", "        # Append the summary text and metadata to the summaries list\n", "        summaries.append({\n", "            \"text\": summary_text,\n", "            \"metadata\": summary_metadata\n", "        })\n", "    \n", "    # Create detailed chunks for each page\n", "    detailed_chunks = []\n", "    for page in pages:\n", "        # Chunk the text of the page\n", "        page_chunks = chunk_text(\n", "            page[\"text\"], \n", "            page[\"metadata\"], \n", "            chunk_size, \n", "            chunk_overlap\n", "        )\n", "        # Extend the detailed_chunks list with the chunks from the current page\n", "        detailed_chunks.extend(page_chunks)\n", "    \n", "    print(f\"Created {len(detailed_chunks)} detailed chunks\")\n", "    \n", "    # Create embeddings for summaries\n", "    print(\"Creating embeddings for summaries...\")\n", "    summary_texts = [summary[\"text\"] for summary in summaries]\n", "    summary_embeddings = create_embeddings(summary_texts)\n", "    \n", "    # Create embeddings for detailed chunks\n", "    print(\"Creating embeddings for detailed chunks...\")\n", "    chunk_texts = [chunk[\"text\"] for chunk in detailed_chunks]\n", "    chunk_embeddings = create_embeddings(chunk_texts)\n", "    \n", "    # Create vector stores\n", "    summary_store = SimpleVectorStore()\n", "    detailed_store = SimpleVectorStore()\n", "    \n", "    # Add summaries to summary store\n", "    for i, summary in enumerate(summaries):\n", "        summary_store.add_item(\n", "            text=summary[\"text\"],\n", "            embedding=summary_embeddings[i],\n", "            metadata=summary[\"metadata\"]\n", "        )\n", "    \n", "    # Add chunks to detailed store\n", "    for i, chunk in enumerate(detailed_chunks):\n", "        detailed_store.add_item(\n", "            text=chunk[\"text\"],\n", "            embedding=chunk_embeddings[i],\n", "            metadata=chunk[\"metadata\"]\n", "        )\n", "    \n", "    print(f\"Created vector stores with {len(summaries)} summaries and {len(detailed_chunks)} chunks\")\n", "    return summary_store, detailed_store"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hierarchical Retrieval"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def retrieve_hierarchically(query, summary_store, detailed_store, k_summaries=3, k_chunks=5):\n", "    \"\"\"\n", "    Retrieve information using hierarchical indices.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        summary_store (SimpleVectorStore): Store of document summaries\n", "        detailed_store (SimpleVectorStore): Store of detailed chunks\n", "        k_summaries (int): Number of summaries to retrieve\n", "        k_chunks (int): Number of chunks to retrieve per summary\n", "        \n", "    Returns:\n", "        List[Dict]: Retrieved chunks with relevance scores\n", "    \"\"\"\n", "    print(f\"Performing hierarchical retrieval for query: {query}\")\n", "    \n", "    # Create query embedding\n", "    query_embedding = create_embeddings(query)\n", "    \n", "    # First, retrieve relevant summaries\n", "    summary_results = summary_store.similarity_search(\n", "        query_embedding, \n", "        k=k_summaries\n", "    )\n", "    \n", "    print(f\"Retrieved {len(summary_results)} relevant summaries\")\n", "    \n", "    # Collect pages from relevant summaries\n", "    relevant_pages = [result[\"metadata\"][\"page\"] for result in summary_results]\n", "    \n", "    # Create a filter function to only keep chunks from relevant pages\n", "    def page_filter(metadata):\n", "        return metadata[\"page\"] in relevant_pages\n", "    \n", "    # Then, retrieve detailed chunks from only those relevant pages\n", "    detailed_results = detailed_store.similarity_search(\n", "        query_embedding, \n", "        k=k_chunks * len(relevant_pages),\n", "        filter_func=page_filter\n", "    )\n", "    \n", "    print(f\"Retrieved {len(detailed_results)} detailed chunks from relevant pages\")\n", "    \n", "    # For each result, add which summary/page it came from\n", "    for result in detailed_results:\n", "        page = result[\"metadata\"][\"page\"]\n", "        matching_summaries = [s for s in summary_results if s[\"metadata\"][\"page\"] == page]\n", "        if matching_summaries:\n", "            result[\"summary\"] = matching_summaries[0][\"text\"]\n", "    \n", "    return detailed_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Response Generation with Context"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def generate_response(query, retrieved_chunks):\n", "    \"\"\"\n", "    Generate a response based on the query and retrieved chunks.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        retrieved_chunks (List[Dict]): Retrieved chunks from hierarchical search\n", "        \n", "    Returns:\n", "        str: Generated response\n", "    \"\"\"\n", "    # Extract text from chunks and prepare context parts\n", "    context_parts = []\n", "    \n", "    for i, chunk in enumerate(retrieved_chunks):\n", "        page_num = chunk[\"metadata\"][\"page\"]  # Get the page number from metadata\n", "        context_parts.append(f\"[Page {page_num}]: {chunk['text']}\")  # Format the chunk text with page number\n", "    \n", "    # Combine all context parts into a single context string\n", "    context = \"\\n\\n\".join(context_parts)\n", "    \n", "    # Define the system message to guide the AI assistant\n", "    system_message = \"\"\"You are a helpful AI assistant answering questions based on the provided context.\n", "Use the information from the context to answer the user's question accurately.\n", "If the context doesn't contain relevant information, acknowledge that.\n", "Include page numbers when referencing specific information.\"\"\"\n", "\n", "    # Generate the response using the OpenAI API\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",  # Specify the model to use\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_message},  # System message to guide the assistant\n", "            {\"role\": \"user\", \"content\": f\"Context:\\n\\n{context}\\n\\nQuestion: {query}\"}  # User message with context and query\n", "        ],\n", "        temperature=0.2  # Set the temperature for response generation\n", "    )\n", "    \n", "    # Return the generated response content\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complete RAG Pipeline with Hierarchical Retrieval"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def hierarchical_rag(query, pdf_path, chunk_size=1000, chunk_overlap=200, \n", "                    k_summaries=3, k_chunks=5, regenerate=False):\n", "    \"\"\"\n", "    Complete hierarchical RAG pipeline.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        pdf_path (str): Path to the PDF document\n", "        chunk_size (int): Size of each detailed chunk\n", "        chunk_overlap (int): Overlap between chunks\n", "        k_summaries (int): Number of summaries to retrieve\n", "        k_chunks (int): Number of chunks to retrieve per summary\n", "        regenerate (bool): Whether to regenerate vector stores\n", "        \n", "    Returns:\n", "        Dict: Results including response and retrieved chunks\n", "    \"\"\"\n", "    # Create store filenames for caching\n", "    summary_store_file = f\"{os.path.basename(pdf_path)}_summary_store.pkl\"\n", "    detailed_store_file = f\"{os.path.basename(pdf_path)}_detailed_store.pkl\"\n", "    \n", "    # Process document and create stores if needed\n", "    if regenerate or not os.path.exists(summary_store_file) or not os.path.exists(detailed_store_file):\n", "        print(\"Processing document and creating vector stores...\")\n", "        # Process the document to create hierarchical indices and vector stores\n", "        summary_store, detailed_store = process_document_hierarchically(\n", "            pdf_path, chunk_size, chunk_overlap\n", "        )\n", "        \n", "        # Save the summary store to a file for future use\n", "        with open(summary_store_file, 'wb') as f:\n", "            pickle.dump(summary_store, f)\n", "        \n", "        # Save the detailed store to a file for future use\n", "        with open(detailed_store_file, 'wb') as f:\n", "            pickle.dump(detailed_store, f)\n", "    else:\n", "        # Load existing summary store from file\n", "        print(\"Loading existing vector stores...\")\n", "        with open(summary_store_file, 'rb') as f:\n", "            summary_store = pickle.load(f)\n", "        \n", "        # Load existing detailed store from file\n", "        with open(detailed_store_file, 'rb') as f:\n", "            detailed_store = pickle.load(f)\n", "    \n", "    # Retrieve relevant chunks hierarchically using the query\n", "    retrieved_chunks = retrieve_hierarchically(\n", "        query, summary_store, detailed_store, k_summaries, k_chunks\n", "    )\n", "    \n", "    # Generate a response based on the retrieved chunks\n", "    response = generate_response(query, retrieved_chunks)\n", "    \n", "    # Return results including the query, response, retrieved chunks, and counts of summaries and detailed chunks\n", "    return {\n", "        \"query\": query,\n", "        \"response\": response,\n", "        \"retrieved_chunks\": retrieved_chunks,\n", "        \"summary_count\": len(summary_store.texts),\n", "        \"detailed_count\": len(detailed_store.texts)\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Standard (Non-Hierarchical) RAG for Comparison"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def standard_rag(query, pdf_path, chunk_size=1000, chunk_overlap=200, k=15):\n", "    \"\"\"\n", "    Standard RAG pipeline without hierarchical retrieval.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        pdf_path (str): Path to the PDF document\n", "        chunk_size (int): Size of each chunk\n", "        chunk_overlap (int): Overlap between chunks\n", "        k (int): Number of chunks to retrieve\n", "        \n", "    Returns:\n", "        Dict: Results including response and retrieved chunks\n", "    \"\"\"\n", "    # Extract pages from the PDF document\n", "    pages = extract_text_from_pdf(pdf_path)\n", "    \n", "    # Create chunks directly from all pages\n", "    chunks = []\n", "    for page in pages:\n", "        # Chunk the text of the page\n", "        page_chunks = chunk_text(\n", "            page[\"text\"], \n", "            page[\"metadata\"], \n", "            chunk_size, \n", "            chunk_overlap\n", "        )\n", "        # Extend the chunks list with the chunks from the current page\n", "        chunks.extend(page_chunks)\n", "    \n", "    print(f\"Created {len(chunks)} chunks for standard RAG\")\n", "    \n", "    # Create a vector store to hold the chunks\n", "    store = SimpleVectorStore()\n", "    \n", "    # Create embeddings for the chunks\n", "    print(\"Creating embeddings for chunks...\")\n", "    texts = [chunk[\"text\"] for chunk in chunks]\n", "    embeddings = create_embeddings(texts)\n", "    \n", "    # Add chunks to the vector store\n", "    for i, chunk in enumerate(chunks):\n", "        store.add_item(\n", "            text=chunk[\"text\"],\n", "            embedding=embeddings[i],\n", "            metadata=chunk[\"metadata\"]\n", "        )\n", "    \n", "    # Create an embedding for the query\n", "    query_embedding = create_embeddings(query)\n", "    \n", "    # Retrieve the most relevant chunks based on the query embedding\n", "    retrieved_chunks = store.similarity_search(query_embedding, k=k)\n", "    print(f\"Retrieved {len(retrieved_chunks)} chunks with standard RAG\")\n", "    \n", "    # Generate a response based on the retrieved chunks\n", "    response = generate_response(query, retrieved_chunks)\n", "    \n", "    # Return the results including the query, response, and retrieved chunks\n", "    return {\n", "        \"query\": query,\n", "        \"response\": response,\n", "        \"retrieved_chunks\": retrieved_chunks\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation Functions"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def compare_approaches(query, pdf_path, reference_answer=None):\n", "    \"\"\"\n", "    Compare hierarchical and standard RAG approaches.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        pdf_path (str): Path to the PDF document\n", "        reference_answer (str, optional): Reference answer for evaluation\n", "        \n", "    Returns:\n", "        Dict: Comparison results\n", "    \"\"\"\n", "    print(f\"\\n=== Comparing RAG approaches for query: {query} ===\")\n", "    \n", "    # Run hierarchical RAG\n", "    print(\"\\nRunning hierarchical RAG...\")\n", "    hierarchical_result = hierarchical_rag(query, pdf_path)\n", "    hier_response = hierarchical_result[\"response\"]\n", "    \n", "    # Run standard RAG\n", "    print(\"\\nRunning standard RAG...\")\n", "    standard_result = standard_rag(query, pdf_path)\n", "    std_response = standard_result[\"response\"]\n", "    \n", "    # Compare results from hierarchical and standard RAG\n", "    comparison = compare_responses(query, hier_response, std_response, reference_answer)\n", "    \n", "    # Return a dictionary with the comparison results\n", "    return {\n", "        \"query\": query,  # The original query\n", "        \"hierarchical_response\": hier_response,  # Response from hierarchical RAG\n", "        \"standard_response\": std_response,  # Response from standard RAG\n", "        \"reference_answer\": reference_answer,  # Reference answer for evaluation\n", "        \"comparison\": comparison,  # Comparison analysis\n", "        \"hierarchical_chunks_count\": len(hierarchical_result[\"retrieved_chunks\"]),  # Number of chunks retrieved by hierarchical RAG\n", "        \"standard_chunks_count\": len(standard_result[\"retrieved_chunks\"])  # Number of chunks retrieved by standard RAG\n", "    }"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def compare_responses(query, hierarchical_response, standard_response, reference=None):\n", "    \"\"\"\n", "    Compare responses from hierarchical and standard RAG.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        hierarchical_response (str): Response from hierarchical RAG\n", "        standard_response (str): Response from standard RAG\n", "        reference (str, optional): Reference answer\n", "        \n", "    Returns:\n", "        str: Comparison analysis\n", "    \"\"\"\n", "    # Define the system prompt to instruct the model on how to evaluate the responses\n", "    system_prompt = \"\"\"You are an expert evaluator of information retrieval systems. \n", "Compare the two responses to the same query, one generated using hierarchical retrieval\n", "and the other using standard retrieval.\n", "\n", "Evaluate them based on:\n", "1. Accuracy: Which response provides more factually correct information?\n", "2. Comprehensiveness: Which response better covers all aspects of the query?\n", "3. Coherence: Which response has better logical flow and organization?\n", "4. Page References: Does either response make better use of page references?\n", "\n", "Be specific in your analysis of the strengths and weaknesses of each approach.\"\"\"\n", "\n", "    # Create the user prompt with the query and both responses\n", "    user_prompt = f\"\"\"Query: {query}\n", "\n", "Response from Hierarchical RAG:\n", "{hierarchical_response}\n", "\n", "Response from Standard RAG:\n", "{standard_response}\"\"\"\n", "\n", "    # If a reference answer is provided, include it in the user prompt\n", "    if reference:\n", "        user_prompt += f\"\"\"\n", "\n", "Reference Answer:\n", "{reference}\"\"\"\n", "\n", "    # Add the final instruction to the user prompt\n", "    user_prompt += \"\"\"\n", "\n", "Please provide a detailed comparison of these two responses, highlighting which approach performed better and why.\"\"\"\n", "\n", "    # Make a request to the OpenAI API to generate the comparison analysis\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},  # System message to guide the assistant\n", "            {\"role\": \"user\", \"content\": user_prompt}  # User message with the query and responses\n", "        ],\n", "        temperature=0  # Set the temperature for response generation\n", "    )\n", "    \n", "    # Return the generated comparison analysis\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def run_evaluation(pdf_path, test_queries, reference_answers=None):\n", "    \"\"\"\n", "    Run a complete evaluation with multiple test queries.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF document\n", "        test_queries (List[str]): List of test queries\n", "        reference_answers (List[str], optional): Reference answers for queries\n", "        \n", "    Returns:\n", "        Dict: Evaluation results\n", "    \"\"\"\n", "    results = []  # Initialize an empty list to store results\n", "    \n", "    # Iterate over each query in the test queries\n", "    for i, query in enumerate(test_queries):\n", "        print(f\"Query: {query}\")  # Print the current query\n", "        \n", "        # Get reference answer if available\n", "        reference = None\n", "        if reference_answers and i < len(reference_answers):\n", "            reference = reference_answers[i]  # Retrieve the reference answer for the current query\n", "        \n", "        # Compare hierarchical and standard RAG approaches\n", "        result = compare_approaches(query, pdf_path, reference)\n", "        results.append(result)  # Append the result to the results list\n", "    \n", "    # Generate overall analysis of the evaluation results\n", "    overall_analysis = generate_overall_analysis(results)\n", "    \n", "    return {\n", "        \"results\": results,  # Return the individual results\n", "        \"overall_analysis\": overall_analysis  # Return the overall analysis\n", "    }"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def generate_overall_analysis(results):\n", "    \"\"\"\n", "    Generate an overall analysis of the evaluation results.\n", "    \n", "    Args:\n", "        results (List[Dict]): Results from individual query evaluations\n", "        \n", "    Returns:\n", "        str: Overall analysis\n", "    \"\"\"\n", "    # Define the system prompt to instruct the model on how to evaluate the results\n", "    system_prompt = \"\"\"You are an expert at evaluating information retrieval systems.\n", "Based on multiple test queries, provide an overall analysis comparing hierarchical RAG \n", "with standard RAG.\n", "\n", "Focus on:\n", "1. When hierarchical retrieval performs better and why\n", "2. When standard retrieval performs better and why\n", "3. The overall strengths and weaknesses of each approach\n", "4. Recommendations for when to use each approach\"\"\"\n", "\n", "    # Create a summary of the evaluations\n", "    evaluations_summary = \"\"\n", "    for i, result in enumerate(results):\n", "        evaluations_summary += f\"Query {i+1}: {result['query']}\\n\"\n", "        evaluations_summary += f\"Hierarchical chunks: {result['hierarchical_chunks_count']}, Standard chunks: {result['standard_chunks_count']}\\n\"\n", "        evaluations_summary += f\"Comparison summary: {result['comparison'][:200]}...\\n\\n\"\n", "\n", "    # Define the user prompt with the evaluations summary\n", "    user_prompt = f\"\"\"Based on the following evaluations comparing hierarchical vs standard RAG across {len(results)} queries, \n", "provide an overall analysis of these two approaches:\n", "\n", "{evaluations_summary}\n", "\n", "Please provide a comprehensive analysis of the relative strengths and weaknesses of hierarchical RAG \n", "compared to standard RAG, with specific focus on retrieval quality and response generation.\"\"\"\n", "\n", "    # Make a request to the OpenAI API to generate the overall analysis\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},  # System message to guide the assistant\n", "            {\"role\": \"user\", \"content\": user_prompt}  # User message with the evaluations summary\n", "        ],\n", "        temperature=0  # Set the temperature for response generation\n", "    )\n", "    \n", "    # Return the generated overall analysis\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation of Hierarchical and Standard RAG Approaches"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing document and creating vector stores...\n", "Extracting text from data/AI_Information.pdf...\n", "Extracted 15 pages with content\n", "Generating page summaries...\n", "Summarizing page 1/15...\n", "Summarizing page 2/15...\n", "Summarizing page 3/15...\n", "Summarizing page 4/15...\n", "Summarizing page 5/15...\n", "Summarizing page 6/15...\n", "Summarizing page 7/15...\n", "Summarizing page 8/15...\n", "Summarizing page 9/15...\n", "Summarizing page 10/15...\n", "Summarizing page 11/15...\n", "Summarizing page 12/15...\n", "Summarizing page 13/15...\n", "Summarizing page 14/15...\n", "Summarizing page 15/15...\n", "Created 47 detailed chunks\n", "Creating embeddings for summaries...\n", "Creating embeddings for detailed chunks...\n", "Created vector stores with 15 summaries and 47 chunks\n", "Performing hierarchical retrieval for query: What are the key applications of transformer models in natural language processing?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9608\\2918097221.py:62: DeprecationWarning: Conversion of an array with ndim > 0 to a scalar is deprecated, and will error in future. Ensure you extract a single element from your array before performing this operation. (Deprecated NumPy 1.25.)\n", "  \"similarity\": float(score)  # Add the similarity score\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Retrieved 3 relevant summaries\n", "Retrieved 10 detailed chunks from relevant pages\n", "\n", "=== Response ===\n", "I couldn't find any information about transformer models in the provided context. The context appears to focus on various applications of Artificial Intelligence (AI) and Machine Learning (ML), including computer vision, deep learning, reinforcement learning, and more. However, transformer models are not mentioned.\n", "\n", "If you're looking for information on transformer models, I'd be happy to try and help you find it. Alternatively, if you have any other questions based on the provided context, I'd be happy to try and assist you.\n", "Query: How do transformers handle sequential data compared to RNNs?\n", "\n", "=== Comparing RAG approaches for query: How do transformers handle sequential data compared to RNNs? ===\n", "\n", "Running hierarchical RAG...\n", "Loading existing vector stores...\n", "Performing hierarchical retrieval for query: How do transformers handle sequential data compared to RNNs?\n", "Retrieved 3 relevant summaries\n", "Retrieved 10 detailed chunks from relevant pages\n", "\n", "Running standard RAG...\n", "Extracting text from data/AI_Information.pdf...\n", "Extracted 15 pages with content\n", "Created 47 chunks for standard RAG\n", "Creating embeddings for chunks...\n", "Retrieved 15 chunks with standard RAG\n", "\n", "=== OVERALL ANALYSIS ===\n", "Based on the provided evaluation, I will provide a comprehensive analysis of the relative strengths and weaknesses of hierarchical RAG compared to standard RAG.\n", "\n", "**Overview of Hierarchical RAG and Standard RAG**\n", "\n", "Hierarchical RAG (Retrieval Algorithm for Generating) is an extension of the standard RAG approach, which involves dividing the input text into smaller chunks or sub-sequences to facilitate more efficient and effective retrieval. The hierarchical approach further divides these chunks into smaller sub-chunks, allowing for more granular and detailed retrieval.\n", "\n", "Standard RAG, on the other hand, uses a single chunk size to retrieve relevant information from the input text.\n", "\n", "**Strengths of Hierarchical RAG**\n", "\n", "1. **Improved Retrieval Quality**: Hierarchical RAG's ability to divide the input text into smaller sub-chunks allows for more precise retrieval, as it can capture subtle nuances and relationships between words and phrases that may be missed by standard RAG.\n", "2. **Enhanced Response Generation**: By considering multiple levels of granularity, hierarchical RAG can generate more accurate and informative responses, as it can take into account the context and relationships between different parts of the input text.\n", "3. **Better Handling of Complex Input Text**: Hierarchical RAG is particularly well-suited for handling complex input text, such as long documents or texts with multiple layers of abstraction.\n", "\n", "**Weaknesses of Hierarchical RAG**\n", "\n", "1. **Increased Computational Complexity**: The hierarchical approach requires more computational resources and processing power, as it needs to handle multiple levels of granularity.\n", "2. **Higher Risk of Overfitting**: The increased number of parameters and complexity of the hierarchical model can lead to overfitting, particularly if the training data is limited or biased.\n", "\n", "**Strengths of Standard RAG**\n", "\n", "1. **Simpler and Faster**: Standard RAG is a simpler and faster approach, as it only requires a single chunk size and less computational resources.\n", "2. **Less Risk of Overfitting**: The standard model has fewer parameters and is less prone to overfitting, making it a more robust and reliable choice.\n", "\n", "**Weaknesses of Standard RAG**\n", "\n", "1. **Limited Retrieval Quality**: Standard RAG's single chunk size can lead to limited retrieval quality, as it may not capture the full range of nuances and relationships between words and phrases.\n", "2. **Less Effective for Complex Input Text**: Standard RAG is less effective for handling complex input text, as it may struggle to capture the context and relationships between different parts of the text.\n", "\n", "**When to Use Each Approach**\n", "\n", "1. **Use Hierarchical RAG**:\n", "\t* When dealing with complex input text, such as long documents or texts with multiple layers of abstraction.\n", "\t* When high retrieval quality and response generation are critical, such as in applications requiring accurate and informative responses.\n", "\t* When computational resources are not a concern, and the benefits of hierarchical retrieval outweigh the costs.\n", "2. **Use Standard RAG**:\n", "\t* When dealing with simple input text, such as short documents or texts with a clear and concise structure.\n", "\t* When computational resources are limited, and speed is a priority.\n", "\t* When the goal is to quickly retrieve relevant information, rather than generating accurate and informative responses.\n", "\n", "In conclusion, hierarchical RAG offers improved retrieval quality and response generation, but at the cost of increased computational complexity and risk of overfitting. Standard RAG, on the other hand, is simpler and faster, but may have limited retrieval quality and be less effective for complex input text. The choice of approach depends on the specific requirements and constraints of the application.\n"]}], "source": ["# Path to the PDF document containing AI information\n", "pdf_path = \"data/AI_Information.pdf\"\n", "\n", "# Example query about AI for testing the hierarchical RAG approach\n", "query = \"What are the key applications of transformer models in natural language processing?\"\n", "result = hierarchical_rag(query, pdf_path)\n", "\n", "print(\"\\n=== Response ===\")\n", "print(result[\"response\"])\n", "\n", "# Test query for formal evaluation (using only one query as requested)\n", "test_queries = [\n", "    \"How do transformers handle sequential data compared to RNNs?\"\n", "]\n", "\n", "# Reference answer for the test query to enable comparison\n", "reference_answers = [\n", "    \"Transformers handle sequential data differently from RNNs by using self-attention mechanisms instead of recurrent connections. This allows transformers to process all tokens in parallel rather than sequentially, capturing long-range dependencies more efficiently and enabling better parallelization during training. Unlike RNNs, transformers don't suffer from vanishing gradient problems with long sequences.\"\n", "]\n", "\n", "# Run the evaluation comparing hierarchical and standard RAG approaches\n", "evaluation_results = run_evaluation(\n", "    pdf_path=pdf_path,\n", "    test_queries=test_queries,\n", "    reference_answers=reference_answers\n", ")\n", "\n", "# Print the overall analysis of the comparison\n", "print(\"\\n=== OVERALL ANALYSIS ===\")\n", "print(evaluation_results[\"overall_analysis\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}