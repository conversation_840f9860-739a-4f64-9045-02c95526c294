{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["# Simple RAG with RL\n", "\n", "[![Python 3.7+](https://img.shields.io/badge/python-3.7+-blue.svg)](https://www.python.org/downloads/release/python-370/) [![Nebius AI](https://img.shields.io/badge/Nebius%20AI-LLM-brightgreen)](https://cloud.nebius.ai/services/llm-embedding) [![OpenAI](https://img.shields.io/badge/OpenAI-API-lightgrey)](https://openai.com/) [![Medium](https://img.shields.io/badge/Medium-Blog-black?logo=medium)](https://medium.com/@fareedkhandev/maximizing-simple-rag-performance-using-rl-rewards-in-python-d4c14cbadf59)\n", "\n", "A simple RAG works in three simple steps:\n", "\n", "1. **Indexing**: Break documents into chunks and convert to vector embeddings.\n", "\n", "2. **Retrieval**: When a question is asked, find the most relevant chunks.\n", "\n", "3. **Generation**: <PERSON>mbine the question with retrieved chunks and let the AI generate an answer using this information.\n", "\n", "The actual problem is to generate an answer to a given question using the provided documents. Simple RAG often fails to generate accurate answers due to the lack of context in the retrieved chunks. In this notebook, we will use the `RL RAG` approach to generate answers to the given questions using the provided documents."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Table of Contents\n", "\n", "- [Setting Up the Environment](#setting-up-the-environment)\n", "- [Data Preprocessing](#data-preprocessing)\n", "- [Document Embedding Generation](#document-embedding-generation)\n", "- [Vector Store Implementation](#vector-store-implementation)\n", "- [Simple Retrieval Implementation](#simple-retrieval-implementation)\n", "  - [Cosine Similarity](#cosine-similarity)\n", "  - [Similarity Search](#similarity-search)\n", "  - [LLM Response Generation](#llm-response-generation)\n", "  - [Basic RAG Pipeline](#basic-rag-pipeline)\n", "  - [Evaluation of Basic RAG](#evaluate-the-basic-rag-pipeline)\n", "- [Reinforcement Learning for RAG](#reinforcement-learning-for-rag)\n", "  - [State, Action Space, and Reward Methodology](#state-action-space-and-reward-methodology)\n", "  - [Policy Network](#policy-network)\n", "  - [Single RL Step](#single-rl-step)\n", "  - [Training Parameters and Policy Update](#training-parameters-and-policy-update)\n", "  - [Training Loop](#training-loop)\n", "  - [Performance Comparison Logic](#performance-comparison-logic)\n", "- [Evaluation Framework](#evaluation-framework)\n", "- [Evaluating RL vs Simple RAG](#evaluating-rl-vs-simple-rag)\n", "- [Saving Comparison Results](#saving-the-comparison-results)\n", "- [Conclusion](#what-can-we-conclude)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "\n", "First, we need to import the necessary libraries and set up the environment. We will be using HuggingFace Models hosted under **Nebius** platform. Obviously, you can use your own models as long as they are compatible with OpenAI's API."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing the os module for interacting with the operating system\n", "import os\n", "\n", "# Importing the OpenAI module for working with OpenAI's API\n", "from openai import OpenAI\n", "\n", "# Importing numpy for numerical operations\n", "import numpy as np\n", "\n", "# Importing json for working with JSON data\n", "import json\n", "\n", "# Typing module for type hints\n", "from typing import Dict, List, Tuple, Optional, Union"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we need to initialize the client responsible for response and embedding generation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set up the API connection using the OpenAI client\n", "# Replace the base_url and api_key with your own values\n", "\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",  # Base URL for (eg. ollama api, anyother llm api provider)\n", "    api_key= os.environ[\"OPENAI_API_KEY\"]  # API key for authentication \n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Preprocessing\n", "Now that we have moved onto the data preprocessing stage, we need to load the data and preprocess it. Let's create a function that will load all the `.txt` files from a directory and return a list of documents."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Function to load documents from a directory\n", "def load_documents(directory_path: str) -> List[str]:\n", "    \"\"\"\n", "    Load all text documents from the specified directory.\n", "\n", "    Args:\n", "        directory_path (str): Path to the directory containing text files.\n", "\n", "    Returns:\n", "        List[str]: A list of strings, where each string is the content of a text file.\n", "    \"\"\"\n", "    documents = []  # Initialize an empty list to store document contents\n", "    for filename in os.listdir(directory_path):  # Iterate through all files in the directory\n", "        if filename.endswith(\".txt\"):  # Check if the file has a .txt extension\n", "            # Open the file in read mode with UTF-8 encoding and append its content to the list\n", "            with open(os.path.join(directory_path, filename), 'r', encoding='utf-8') as file:\n", "                documents.append(file.read())\n", "    return documents  # Return the list of document contents"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We need to create a function that performs chunking of the documents once they are loaded. We are using a `chunk_size` of `100` characters, but you can adjust it as per your requirements."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Function to split documents into chunks\n", "def split_into_chunks(documents: List[str], chunk_size: int = 30) -> List[str]:\n", "    \"\"\"\n", "    Split documents into smaller chunks of specified size.\n", "\n", "    Args:\n", "        documents (List[str]): A list of document strings to be split into chunks.\n", "        chunk_size (int): The maximum number of words in each chunk. Default is 100.\n", "\n", "    Returns:\n", "        List[str]: A list of chunks, where each chunk is a string containing up to `chunk_size` words.\n", "    \"\"\"\n", "    chunks = []  # Initialize an empty list to store the chunks\n", "    for doc in documents:  # Iterate through each document\n", "        words = doc.split()  # Split the document into words\n", "        # Create chunks of the specified size\n", "        for i in range(0, len(words), chunk_size):\n", "            chunk = \" \".join(words[i:i + chunk_size])  # Join words to form a chunk\n", "            chunks.append(chunk)  # Add the chunk to the list\n", "    return chunks  # Return the list of chunks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This step is **optional**, where we preprocess each chunk by removing special characters, converting to lowercase, etc."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Function to preprocess text (e.g., lowercasing, removing special characters)\n", "def preprocess_text(text: str) -> str:\n", "    \"\"\"\n", "    Preprocess the input text by converting it to lowercase and removing special characters.\n", "\n", "    Args:\n", "        text (str): The input text to preprocess.\n", "\n", "    Returns:\n", "        str: The preprocessed text with only alphanumeric characters and spaces.\n", "    \"\"\"\n", "    # Convert the text to lowercase\n", "    text = text.lower()\n", "    # Remove special characters, keeping only alphanumeric characters and spaces\n", "    text = ''.join(char for char in text if char.isalnum() or char.isspace())\n", "    return text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["However, if you are using the previous preprocessing step, you can simply create a function to preprocess the entire document."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Function to preprocess all chunks\n", "def preprocess_chunks(chunks: List[str]) -> List[str]:\n", "    \"\"\"\n", "    Apply preprocessing to all text chunks.\n", "\n", "    Args:\n", "        chunks (List[str]): A list of text chunks to preprocess.\n", "\n", "    Returns:\n", "        List[str]: A list of preprocessed text chunks.\n", "    \"\"\"\n", "    # Apply the preprocess_text function to each chunk in the list\n", "    return [preprocess_text(chunk) for chunk in chunks]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have implemented all the functions for data preprocessing, we can load the documents from the directory, split them into chunks, and preprocess the chunks."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Specify the directory path containing the text files\n", "directory_path = \"data\"\n", "\n", "# Load all text documents from the specified directory\n", "documents = load_documents(directory_path)\n", "\n", "# Split the loaded documents into smaller chunks of text\n", "chunks = split_into_chunks(documents)\n", "\n", "# Preprocess the chunks (e.g., lowercasing, removing special characters)\n", "preprocessed_chunks = preprocess_chunks(chunks)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Print the first 200 characters of the first two chunks"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chunk 1: quantum computing principles progress and possibil ... \n", "--------------------------------------------------\n", "Chunk 2: process information in binary digits bits quantum  ... \n", "--------------------------------------------------\n"]}], "source": ["# Print the first 2 preprocessed chunks, displaying only the first 200 characters of each chunk\n", "for i in range(2):\n", "    # Use slicing to limit the output to the first 200\n", "    print(f\"Chunk {i+1}: {preprocessed_chunks[i][:50]} ... \")\n", "    print(\"-\" * 50)  # Print a separator line"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Embedding Generation\n", "\n", "In the previous step, we chunked our document. Now it's time to generate embeddings for the chunk dataset. When working with RAG, our knowledge base is typically quite large. Therefore, we need to perform embedding generation in batches. Let's create a core function to generate embeddings for the chunks in batches.\n", "\n", "The embedding model we are using is `BAAI/bge-en-icl`."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Function to generate embeddings for a single batch of text chunks\n", "def generate_embeddings_batch(chunks_batch: List[str], model: str = \"BAAI/bge-en-icl\") -> List[List[float]]:\n", "    \"\"\"\n", "    Generate embeddings for a batch of text chunks using the OpenAI client.\n", "\n", "    Args:\n", "        chunks_batch (List[str]): A batch of text chunks to generate embeddings for.\n", "        model (str): The model to use for embedding generation. Default is \"BAAI/bge-en-icl\".\n", "\n", "    Returns:\n", "        List[List[float]]: A list of embeddings, where each embedding is a list of floats.\n", "    \"\"\"\n", "    # Use the OpenAI client to create embeddings for the input batch\n", "    response = client.embeddings.create(\n", "        model=model,  # Specify the model to use for embedding generation\n", "        input=chunks_batch  # Provide the batch of text chunks as input\n", "    )\n", "    # Extract embeddings from the response and return them\n", "    embeddings = [item.embedding for item in response.data]\n", "    return embeddings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we will define a function to generate embeddings for all text chunks in batches. This function will take a list of text chunks as input and generate embeddings for each batch of chunks using the OpenAI client. The function will return a list of embeddings corresponding to all the text chunks."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Function to generate embeddings for all chunks with batching\n", "def generate_embeddings(chunks: List[str], batch_size: int = 10) -> np.ndarray:\n", "    \"\"\"\n", "    Generate embeddings for all text chunks in batches.\n", "\n", "    Args:\n", "        chunks (List[str]): A list of text chunks to generate embeddings for.\n", "        batch_size (int): The number of chunks to process in each batch. Default is 10.\n", "\n", "    Returns:\n", "        np.ndarray: A NumPy array containing embeddings for all chunks.\n", "    \"\"\"\n", "    all_embeddings = []  # Initialize an empty list to store all embeddings\n", "\n", "    # Iterate through the chunks in batches\n", "    for i in range(0, len(chunks), batch_size):\n", "        # Extract the current batch of chunks\n", "        batch = chunks[i:i + batch_size]\n", "        # Generate embeddings for the current batch\n", "        embeddings = generate_embeddings_batch(batch)\n", "        # Extend the list of all embeddings with the embeddings from the current batch\n", "        all_embeddings.extend(embeddings)\n", "\n", "    # Convert the list of embeddings to a NumPy array and return it\n", "    return np.array(all_embeddings)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's create another function to save the embeddings to a file in JSON format."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Function to save embeddings to a file\n", "def save_embeddings(embeddings: np.ndarray, output_file: str) -> None:\n", "    \"\"\"\n", "    Save embeddings to a JSON file.\n", "\n", "    Args:\n", "        embeddings (np.ndarray): A NumPy array containing the embeddings to save.\n", "        output_file (str): The path to the output JSON file where embeddings will be saved.\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    # Open the specified file in write mode with UTF-8 encoding\n", "    with open(output_file, 'w', encoding='utf-8') as file:\n", "        # Convert the NumPy array to a list and save it as JSON\n", "        json.dump(embeddings.tolist(), file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have implemented all the functions for embedding generation, we can proceed to generate embeddings for the preprocessed text chunks and save them to a JSON file."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Ensure the chunks are preprocessed before generating embeddings\n", "preprocessed_chunks = preprocess_chunks(chunks)\n", "\n", "# Generate embeddings for the preprocessed chunks\n", "embeddings = generate_embeddings(preprocessed_chunks)\n", "\n", "# Save the generated embeddings to a JSON file named \"embeddings.json\"\n", "save_embeddings(embeddings, \"embeddings.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vector Store Implementation\n", "Since we are not using any python libraries for vector storage, we will implement a simple vector store using a dictionary."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Initialize an in-memory vector store as a dictionary\n", "# The keys will be unique identifiers (integers), and the values will be dictionaries containing embeddings and corresponding text chunks\n", "vector_store: dict[int, dict[str, object]] = {}\n", "\n", "# Function to add embeddings and corresponding text chunks to the vector store\n", "def add_to_vector_store(embeddings: np.ndarray, chunks: List[str]) -> None:\n", "    \"\"\"\n", "    Add embeddings and their corresponding text chunks to the vector store.\n", "\n", "    Args:\n", "        embeddings (np.ndarray): A NumPy array containing the embeddings to add.\n", "        chunks (List[str]): A list of text chunks corresponding to the embeddings.\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    # Iterate over embeddings and chunks simultaneously\n", "    for embedding, chunk in zip(embeddings, chunks):\n", "        # Add each embedding and its corresponding chunk to the vector store\n", "        # Use the current length of the vector store as the unique key\n", "        vector_store[len(vector_store)] = {\"embedding\": embedding, \"chunk\": chunk}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple Retrieval Implementation\n", "\n", "We do know for retrieving the most similar text chunks to a given query, we can use the cosine similarity between the query embedding and the embeddings of all text chunks. The higher the cosine similarity, the more similar the text chunks are. We can then sort the chunks based on their similarity scores and return the top-k most similar chunks.\n", "    \n", "So, let's implement a simple cosine similarity-based retrieval function."]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "tex"}}, "source": ["The cosine similarity between two vectors $A$ and $B$ is calculated as:\n", "\n", "$$\\text{cosine similarity} = \\frac{A \\cdot B}{||A|| \\times ||B||} = \\frac{\\sum_{i=1}^{n} A_i B_i}{\\sqrt{\\sum_{i=1}^{n} A_i^2} \\times \\sqrt{\\sum_{i=1}^{n} B_i^2}}$$\n", "\n", "Where:\n", "- $A \\cdot B$ is the dot product of vectors $A$ and $B$\n", "- $||A||$ and $||B||$ are the Euclidean norms (magnitudes) of the vectors\n", "- $n$ is the dimension of the vectors"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Function to compute cosine similarity between two vectors\n", "def cosine_similarity(vec1: np.ndarray, vec2: np.ndarray) -> float:\n", "    \"\"\"\n", "    Compute the cosine similarity between two vectors.\n", "\n", "    Args:\n", "        vec1 (np.n<PERSON><PERSON>): The first vector.\n", "        vec2 (np.n<PERSON><PERSON>): The second vector.\n", "\n", "    Returns:\n", "        float: The cosine similarity between the two vectors, ranging from -1 to 1.\n", "    \"\"\"\n", "    # Compute the dot product of the two vectors\n", "    dot_product = np.dot(vec1, vec2)\n", "    # Compute the magnitude (norm) of the first vector\n", "    norm_vec1 = np.linalg.norm(vec1)\n", "    # Compute the magnitude (norm) of the second vector\n", "    norm_vec2 = np.linalg.norm(vec2)\n", "    # Return the cosine similarity as the ratio of the dot product to the product of the norms\n", "    return dot_product / (norm_vec1 * norm_vec2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When we calculate the cosine similarity between a query and all the chunks, we can perform a similarity search. Based on the `top_k` parameter, we retrieve the top k most similar chunks."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# Function to perform similarity search in the vector store\n", "def similarity_search(query_embedding: np.n<PERSON>ray, top_k: int = 5) -> List[str]:\n", "    \"\"\"\n", "    Perform similarity search in the vector store and return the top_k most similar chunks.\n", "\n", "    Args:\n", "        query_embedding (np.ndarray): The embedding vector of the query.\n", "        top_k (int): The number of most similar chunks to retrieve. Default is 5.\n", "\n", "    Returns:\n", "        List[str]: A list of the top_k most similar text chunks.\n", "    \"\"\"\n", "    similarities = []  # Initialize a list to store similarity scores and corresponding keys\n", "\n", "    # Iterate through all items in the vector store\n", "    for key, value in vector_store.items():\n", "        # Compute the cosine similarity between the query embedding and the stored embedding\n", "        similarity = cosine_similarity(query_embedding, value[\"embedding\"])\n", "        # Append the key and similarity score as a tuple to the list\n", "        similarities.append((key, similarity))\n", "\n", "    # Sort the list of similarities in descending order based on the similarity score\n", "    similarities = sorted(similarities, key=lambda x: x[1], reverse=True)\n", "\n", "    # Retrieve the top_k most similar chunks based on their keys\n", "    return [vector_store[key][\"chunk\"] for key, _ in similarities[:top_k]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once we have the similarity search function ready, we can simply code a retrieval function on top of it that will provide the relevant chunks based on the query."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Function to retrieve relevant document chunks for a query\n", "def retrieve_relevant_chunks(query_text: str, top_k: int = 5) -> List[str]:\n", "    \"\"\"\n", "    Retrieve the most relevant document chunks for a given query text.\n", "\n", "    Args:\n", "        query_text (str): The query text for which relevant chunks are to be retrieved.\n", "        top_k (int): The number of most relevant chunks to retrieve. Default is 5.\n", "\n", "    Returns:\n", "        List[str]: A list of the top_k most relevant text chunks.\n", "    \"\"\"\n", "    # Generate embedding for the query text using the embedding model\n", "    query_embedding = generate_embeddings([query_text])[0]\n", "    \n", "    # Perform similarity search to find the most relevant chunks\n", "    relevant_chunks = similarity_search(query_embedding, top_k=top_k)\n", "    \n", "    # Return the list of relevant chunks\n", "    return relevant_chunks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have implemented all the functions for retrieval, we can proceed to test the retrieval system with a sample query."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chunk 1: quantum computing principles progress and possibil ... \n", "--------------------------------------------------\n", "Chunk 2: through distinct stages 1 nisq era current 2 error ... \n", "--------------------------------------------------\n", "Chunk 3: quantum advantage and practical applications quant ... \n", "--------------------------------------------------\n", "Chunk 4: process information in binary digits bits quantum  ... \n", "--------------------------------------------------\n", "Chunk 5: measuring the correct answer quantum gates and cir ... \n", "--------------------------------------------------\n"]}], "source": ["# Add the generated embeddings and their corresponding preprocessed chunks to the vector store\n", "add_to_vector_store(embeddings, preprocessed_chunks)\n", "\n", "# Define a query text for which we want to retrieve relevant document chunks\n", "query_text = \"What is Quantum Computing?\"\n", "\n", "# Retrieve the most relevant chunks from the vector store based on the query text\n", "relevant_chunks = retrieve_relevant_chunks(query_text)\n", "\n", "# Print the first 50 characters of each retrieved relevant chunk\n", "for idx, chunk in enumerate(relevant_chunks):\n", "    print(f\"Chunk {idx + 1}: {chunk[:50]} ... \")\n", "    print(\"-\" * 50)  # Print a separator line"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## LLM Response Generation\n", "\n", "When we have a query and a set of relevant document chunks, we can use a large language model (LLM) to generate a response based on the query and the retrieved information. In this section, we will use the OpenAI API to generate a response to a query by providing the query text and the relevant document chunks as context to the LLM.\n", "\n", "First we need a function to construct the input prompt for the LLM, which includes the query text and the relevant document chunks as context."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Function to construct a prompt with context\n", "def construct_prompt(query: str, context_chunks: List[str]) -> str:\n", "    \"\"\"\n", "    Construct a prompt by combining the query with the retrieved context chunks.\n", "\n", "    Args:\n", "        query (str): The query text for which the prompt is being constructed.\n", "        context_chunks (List[str]): A list of relevant context chunks to include in the prompt.\n", "\n", "    Returns:\n", "        str: The constructed prompt to be used as input for the LLM.\n", "    \"\"\"\n", "    # Combine all context chunks into a single string, separated by newlines\n", "    context = \"\\n\".join(context_chunks)\n", "    \n", "    # Define the system message to guide the LLM's behavior\n", "    system_message = (\n", "        \"You are a helpful assistant. Only use the provided context to answer the question. \"\n", "        \"If the context doesn't contain the information needed, say 'I don't have enough information to answer this question.'\"\n", "    )\n", "    \n", "    # Construct the final prompt by combining the system message, context, and query\n", "    prompt = f\"System: {system_message}\\n\\nContext:\\n{context}\\n\\nQuestion:\\n{query}\\n\\nAnswer:\"\n", "    \n", "    return prompt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To generate an LLM response, we need to implement a function that takes the constructed input prompt and sends it to the OpenAI API for response generation."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Function to generate a response using the OpenAI chat model\n", "def generate_response(\n", "    prompt: str,\n", "    model: str = \"google/gemma-2-2b-it\",\n", "    max_tokens: int = 512,\n", "    temperature: float = 1,\n", "    top_p: float = 0.9,\n", "    top_k: int = 50\n", ") -> str:\n", "    \"\"\"\n", "    Generate a response from the OpenAI chat model based on the constructed prompt.\n", "\n", "    Args:\n", "        prompt (str): The input prompt to provide to the chat model.\n", "        model (str): The model to use for generating the response. Default is \"google/gemma-2-2b-it\".\n", "        max_tokens (int): Maximum number of tokens in the response. Default is 512.\n", "        temperature (float): Sampling temperature for response diversity. Default is 0.5.\n", "        top_p (float): Probability mass for nucleus sampling. Default is 0.9.\n", "        top_k (int): Number of highest probability tokens to consider. Default is 50.\n", "\n", "    Returns:\n", "        str: The generated response from the chat model.\n", "    \"\"\"\n", "    # Use the OpenAI client to create a chat completion\n", "    response = client.chat.completions.create(\n", "        model=model,  # Specify the model to use for generating the response\n", "        max_tokens=max_tokens,  # Maximum number of tokens in the response\n", "        temperature=temperature,  # Sampling temperature for response diversity\n", "        top_p=top_p,  # Probability mass for nucleus sampling\n", "        extra_body={  # Additional parameters for the request\n", "            \"top_k\": top_k  # Number of highest probability tokens to consider\n", "        },\n", "        messages=[  # List of messages to provide context for the chat model\n", "            {\n", "                \"role\": \"user\",  # Role of the message sender (user in this case)\n", "                \"content\": [  # Content of the message\n", "                    {\n", "                        \"type\": \"text\",  # Type of content (text in this case)\n", "                        \"text\": prompt  # The actual prompt text\n", "                    }\n", "                ]\n", "            }\n", "        ]\n", "    )\n", "    # Return the content of the first choice in the response\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic RAG Pipeline\n", "\n", "We cannot run small pieces of code repeatedly. Therefore, we need to create a simple RAG pipeline that takes only one parameter, which is our query, and returns the LLM response."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# Function to implement the basic Retrieval-Augmented Generation (RAG) pipeline\n", "def basic_rag_pipeline(query: str) -> str:\n", "    \"\"\"\n", "    Implement the basic Retrieval-Augmented Generation (RAG) pipeline:\n", "    retrieve relevant chunks, construct a prompt, and generate a response.\n", "\n", "    Args:\n", "        query (str): The input query for which a response is to be generated.\n", "\n", "    Returns:\n", "        str: The generated response from the LLM based on the query and retrieved context.\n", "    \"\"\"\n", "    # Step 1: Retrieve the most relevant chunks for the given query\n", "    relevant_chunks: List[str] = retrieve_relevant_chunks(query)\n", "    \n", "    # Step 2: Construct a prompt using the query and the retrieved chunks\n", "    prompt: str = construct_prompt(query, relevant_chunks)\n", "    \n", "    # Step 3: Generate a response from the LLM using the constructed prompt\n", "    response: str = generate_response(prompt)\n", "    \n", "    # Return the generated response\n", "    return response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate the basic RAG pipeline\n", "\n", "Now that we have coded the basic RAG pipeline, we can use it for evaluation. Our evaluation queries contain different targeted segments, such as `factual_queries` and `complex_nature`. We are going to test the factual knowledge of our RAG pipeline.\n", "\n", "Let's load our evaluation queries and their expected answers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample Query: What is the mathematical representation of a qubit in superposition?\n", "\n", "Expected Answer: |ÏˆâŸ© = Î±|0âŸ© + Î²|1âŸ©, where Î± and Î² are complex numbers satisfying |Î±|Â² + |Î²|Â² = 1, representing the probability amplitudes for measuring the qubit in state |0âŸ© or |1âŸ© respectively.\n", "\n"]}], "source": ["# Open the validation data file in read mode and load its content as a dictionary\n", "with open('data/val_rl.json', 'r') as file:\n", "    validation_data = json.load(file)\n", "\n", "# Test the basic RAG pipeline with a sample query\n", "sample_query = validation_data['basic_factual_questions'][0]['question']  # Extract the query text\n", "expected_answer = validation_data['basic_factual_questions'][0]['answer']  # Extract the ground truth answer\n", "\n", "# print the sample query and expected answer\n", "print(f\"Sample Query: {sample_query}\\n\")\n", "print(f\"Expected Answer: {expected_answer}\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's test the basic RAG pipeline with this eval query and see how well it performs."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Running the Retrieval-Augmented Generation (RAG) pipeline...\n", "📥 Query: What is the mathematical representation of a qubit in superposition?\n", "\n", "🤖 AI Response:\n", "--------------------------------------------------\n", "ψ  α0  β1\n", "--------------------------------------------------\n", "✅ Ground Truth Answer:\n", "--------------------------------------------------\n", "|ÏˆâŸ© = Î±|0âŸ© + Î²|1âŸ©, where Î± and Î² are complex numbers satisfying |Î±|Â² + |Î²|Â² = 1, representing the probability amplitudes for measuring the qubit in state |0âŸ© or |1âŸ© respectively.\n", "--------------------------------------------------\n"]}], "source": ["# Print a message to indicate the start of the RAG pipeline\n", "print(\"🔍 Running the Retrieval-Augmented Generation (RAG) pipeline...\")\n", "print(f\"📥 Query: {sample_query}\\n\")\n", "\n", "# Run the RAG pipeline and get the response\n", "response = basic_rag_pipeline(sample_query)\n", "\n", "# Print the response with better formatting\n", "print(\"🤖 AI Response:\")\n", "print(\"-\" * 50)\n", "print(response.strip())\n", "print(\"-\" * 50)\n", "\n", "# Print the ground truth answer for comparison\n", "print(\"✅ Ground Truth Answer:\")\n", "print(\"-\" * 50)\n", "print(expected_answer)\n", "print(\"-\" * 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The simple RAG pipeline doesn't seem to perform well in its current state. The generated response is not only irrelevant to the ground truth but also misses critical information.\n", "\n", "But don't worry! In the upcoming steps, we will implement a Reinforcement Learning-based RAG pipeline to address these shortcomings. This will help us improve the retrieval and generation process, making the responses more accurate and contextually relevant.\n", "\n", "Stay tuned as we take our RAG pipeline to the next level! 🚀"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "julia"}}, "source": ["## Reinforcement Learning for RAG\n", "\n", "Reinforcement Learning (RL) is a type of machine learning where an agent learns to make decisions by taking actions in an environment to maximize some notion of cumulative reward. Unlike supervised learning, the agent is not explicitly told which actions to take, but instead must discover which actions yield the most reward through trial and error.\n", "\n", "Follow are the main components of a reinforcement learning system:\n", "\n", "1. **Agent**: The learner or decision-maker\n", "2. **Environment**: The world with which the agent interacts\n", "3. **State (S)**: The current situation of the agent in the environment\n", "4. **Action (A)**: A set of possible moves the agent can make\n", "5. **<PERSON><PERSON> (R)**: Feedback from the environment after each action\n", "6. **Policy (π)**: Strategy that the agent follows to determine the next action\n", "\n", "The goal in reinforcement learning is to learn a policy π that maximizes the expected cumulative reward:\n", "\n", "$$\\pi^* = \\arg\\max_\\pi \\mathbb{E}\\left[ \\sum_{t=0}^{T} \\gamma^t R_t \\right]$$\n", "\n", "Where:\n", "- $\\pi^*$ is the optimal policy\n", "- $\\gamma$ is the discount factor (0 ≤ γ ≤ 1)\n", "- $R_t$ is the reward at time step t\n", "- $T$ is the final time step\n", "\n", "In the context of RAG systems, reinforcement learning can be used to:\n", "- Improve retrieval by learning which documents are most helpful\n", "- Refine prompt construction based on user feedback\n", "- Optimize the generation process by learning from successful responses"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## State, Action Space, and Reward Methodology\n", "\n", "The very first step when coding an RL algorithm is to define three things:\n", "\n", "- **State**: It is the current situation of the environment. In our case, the initial state is our simple RAG pipeline (query, context, response).\n", "- **Action Space**: It is the decision that the agent takes based on the state. In our case, the actions can include changing the model, modifying the context, altering the query, etc.\n", "- **Reward**: It is the feedback that the agent receives after taking an action. In our case, the reward can be the similarity between the generated response and the ground truth answer."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Our state will be changing constantly as we perform training. For that, we need to save the state after each `training episode` so that our RL agent can learn from it and avoid making the same mistakes again."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Function to define the state representation for reinforcement learning\n", "def define_state(\n", "    query: str, \n", "    context_chunks: List[str], \n", "    rewritten_query: str = None, \n", "    previous_responses: List[str] = None, \n", "    previous_rewards: List[float] = None\n", ") -> dict:\n", "    \"\"\"\n", "    Define the state representation for the reinforcement learning agent.\n", "    \n", "    Args:\n", "        query (str): The original user query.\n", "        context_chunks (List[str]): Retrieved context chunks from the knowledge base.\n", "        rewritten_query (str, optional): A reformulated version of the original query.\n", "        previous_responses (List[str], optional): List of previously generated responses.\n", "        previous_rewards (List[float], optional): List of rewards received for previous actions.\n", "    \n", "    Returns:\n", "        dict: A dictionary representing the current state with all relevant information.\n", "    \"\"\"\n", "    state = {\n", "        \"original_query\": query,                                    # The initial query from the user\n", "        \"current_query\": rewritten_query if rewritten_query else query,  # Current version of the query (may be rewritten)\n", "        \"context\": context_chunks,                                 # Retrieved context chunks from the knowledge base\n", "        \"previous_responses\": previous_responses if previous_responses else [],  # History of generated responses\n", "        \"previous_rewards\": previous_rewards if previous_rewards else []         # History of received rewards\n", "    }\n", "    return state"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We have defined the state representation for the RL agent, including the user query, retrieved context chunks, rewritten query (if any), and histories of responses and rewards. This state will guide the agent in generating better responses. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we need to define the action space for the reinforcement learning agent. The action space consists of the set of possible actions that the agent can take at each step. In this case, we define four actions:\n", "- `rewrite_query`: Reformulate the original query to improve retrieval\n", "- `expand_context`: Retrieve additional context chunks\n", "- `filter_context`: Remove irrelevant context chunks\n", "- `generate_response`: Generate a response based on the current query and context"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Function to define the action space for reinforcement learning\n", "def define_action_space() -> List[str]:\n", "    \"\"\"\n", "    Define the set of possible actions the reinforcement learning agent can take.\n", "    \n", "    Actions include:\n", "    - rewrite_query: Reformulate the original query to improve retrieval\n", "    - expand_context: Retrieve additional context chunks\n", "    - filter_context: Remove irrelevant context chunks\n", "    - generate_response: Generate a response based on current query and context\n", "    \n", "    Returns:\n", "        List[str]: A list of available actions.\n", "    \"\"\"\n", "\n", "    # Define the set of actions the agent can take\n", "    actions = [\"rewrite_query\", \"expand_context\", \"filter_context\", \"generate_response\"]\n", "    return actions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Obviously, when our RL agent takes an action, it will be based on the current state and the action space. It will be rewarded based on the quality of the response generated by the RAG pipeline. The reward function will be based on the cosine similarity between the generated response and the ground truth answer."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# Function to calculate the reward based on response quality\n", "def calculate_reward(response: str, ground_truth: str) -> float:\n", "    \"\"\"\n", "    Calculate a reward value by comparing the generated response to the ground truth.\n", "    \n", "    Uses cosine similarity between the embeddings of the response and ground truth\n", "    to determine how close the response is to the expected answer.\n", "    \n", "    Args:\n", "        response (str): The generated response from the RAG pipeline.\n", "        ground_truth (str): The expected correct answer.\n", "    \n", "    Returns:\n", "        float: A reward value between -1 and 1, where higher values indicate \n", "               greater similarity to the ground truth.\n", "    \"\"\"\n", "    # Generate embeddings for both the response and ground truth\n", "    response_embedding = generate_embeddings([response])[0]\n", "    ground_truth_embedding = generate_embeddings([ground_truth])[0]\n", "    \n", "    # Calculate cosine similarity between the embeddings as the reward\n", "    similarity = cosine_similarity(response_embedding, ground_truth_embedding)\n", "    return similarity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Our goal is to maximize the reward by generating responses that are similar to the ground truth answer. Higher reward values indicate that the generated response is more aligned with the expected answer."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Action Function Logic\n", "\n", "Now that we have defined the action space, we need to implement the logic for each action. This logic will determine how the RAG pipeline should be modified based on the action taken by the RL agent.\n", "\n", "Just to revisit, the four actions are:\n", "- `rewrite_query`: Reformulate the original query to improve retrieval\n", "- `expand_context`: Retrieve additional context chunks\n", "- `filter_context`: Remove irrelevant context chunks\n", "- `generate_response`: Generate a response based on the current query and context"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's create our first action logic for the agent. The first action we will implement is the `rewrite_query` action, which involves reformulating the original user query to improve retrieval performance. This action is crucial for enhancing the relevance of the retrieved context and generating more accurate responses."]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# Function to rewrite the query for better document retrieval\n", "def rewrite_query(\n", "    query: str, \n", "    context_chunks: List[str], \n", "    model: str = \"google/gemma-2-2b-it\", \n", "    max_tokens: int = 100, \n", "    temperature: float = 0.3\n", ") -> str:\n", "    \"\"\"\n", "    Use the LLM to rewrite the query for better document retrieval.\n", "\n", "    Args:\n", "        query (str): The original query text.\n", "        context_chunks (List[str]): A list of context chunks retrieved so far.\n", "        model (str): The model to use for generating the rewritten query. De<PERSON>ult is \"google/gemma-2-2b-it\".\n", "        max_tokens (int): Maximum number of tokens in the rewritten query. Default is 100.\n", "        temperature (float): Sampling temperature for response diversity. Default is 0.3.\n", "\n", "    Returns:\n", "        str: The rewritten query optimized for document retrieval.\n", "    \"\"\"\n", "    # Construct a prompt for the LLM to rewrite the query\n", "    rewrite_prompt = f\"\"\"\n", "    You are a query optimization assistant. Your task is to rewrite the given query to make it more effective \n", "    for retrieving relevant information. The query will be used for document retrieval.\n", "    \n", "    Original query: {query}\n", "    \n", "    Based on the context retrieved so far:\n", "    {' '.join(context_chunks[:2]) if context_chunks else 'No context available yet'}\n", "    \n", "    Rewrite the query to be more specific and targeted to retrieve better information.\n", "    Rewritten query:\n", "    \"\"\"\n", "    \n", "    # Use the LLM to generate a rewritten query\n", "    response = client.chat.completions.create(\n", "        model=model, # Specify the model to use for generating the response\n", "        max_tokens=max_tokens, # Maximum number of tokens in the response\n", "        temperature=temperature, # Sampling temperature for response diversity\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": rewrite_prompt\n", "            }\n", "        ]\n", "    )\n", "    \n", "    # Extract and return the rewritten query from the response\n", "    rewritten_query = response.choices[0].message.content.strip()\n", "    return rewritten_query"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This action is crucial for enhancing the relevance of the retrieved context and generating more accurate responses."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's code our next action logic, which is to expand the context by retrieving additional chunks. We will use the existing function `retrieve_relevant_chunks` to get more context chunks and then filter out any duplicates from the current context. We will limit the number of new chunks to be added to the context to a specified top_k value."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# Function to expand the context by retrieving additional chunks\n", "def expand_context(query: str, current_chunks: List[str], top_k: int = 3) -> List[str]:\n", "    \"\"\"\n", "    Expand the context by retrieving additional chunks.\n", "\n", "    Args:\n", "        query (str): The query text for which additional context is needed.\n", "        current_chunks (List[str]): The current list of context chunks.\n", "        top_k (int): The number of additional chunks to retrieve. Default is 3.\n", "\n", "    Returns:\n", "        List[str]: The expanded list of context chunks including new unique chunks.\n", "    \"\"\"\n", "    # Retrieve more chunks than currently available\n", "    additional_chunks = retrieve_relevant_chunks(query, top_k=top_k + len(current_chunks))\n", "    \n", "    # Filter out chunks that are already in the current context\n", "    new_chunks = []\n", "    for chunk in additional_chunks:\n", "        if chunk not in current_chunks:\n", "            new_chunks.append(chunk)\n", "    \n", "    # Add new unique chunks to the current context, limited to top_k\n", "    expanded_context = current_chunks + new_chunks[:top_k]\n", "    return expanded_context"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We need to filter the context to keep only the most relevant chunks for the query. This filtering step is crucial to ensure that the context provided to the language model is concise and focused on the most relevant information."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# Function to filter the context to keep only the most relevant chunks\n", "def filter_context(query: str, context_chunks: List[str]) -> List[str]:\n", "    \"\"\"\n", "    Filter the context to keep only the most relevant chunks.\n", "\n", "    Args:\n", "        query (str): The query text for which relevance is calculated.\n", "        context_chunks (List[str]): The list of context chunks to filter.\n", "\n", "    Returns:\n", "        List[str]: A filtered list of the most relevant context chunks.\n", "    \"\"\"\n", "    if not context_chunks:\n", "        return []\n", "        \n", "    # Generate embeddings for the query and each chunk\n", "    query_embedding = generate_embeddings([query])[0]\n", "    chunk_embeddings = [generate_embeddings([chunk])[0] for chunk in context_chunks]\n", "    \n", "    # Calculate relevance scores for each chunk\n", "    relevance_scores = []\n", "    for chunk_embedding in chunk_embeddings:\n", "        score = cosine_similarity(query_embedding, chunk_embedding)\n", "        relevance_scores.append(score)\n", "    \n", "    # Sort chunks by relevance scores in descending order\n", "    sorted_chunks = [x for _, x in sorted(zip(relevance_scores, context_chunks), reverse=True)]\n", "    \n", "    # Keep the top 5 most relevant chunks or fewer if less than 5 are available\n", "    filtered_chunks = sorted_chunks[:min(5, len(sorted_chunks))]\n", "    \n", "    return filtered_chunks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This action will help the agent explore more information relevant to the query."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Policy Network\n", "\n", "Previously, we defined our state, actions, and reward logic. Next, we need to create a policy network that will select an action based on the current state.\n", "\n", "A policy network is a function that takes the current state and the action space as input and returns the selected action based on the state.\n", "\n", "The policy network can use a simple heuristic to select an action based on the current state. For example, if there are no previous responses, the policy network can prioritize rewriting the query. If the context has too many chunks, the policy network can choose to filter the context."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# Function to define a policy network to select an action based on the state\n", "def policy_network(\n", "    state: dict, \n", "    action_space: List[str], \n", "    epsilon: float = 0.2\n", ") -> str:\n", "    \"\"\"\n", "    Define a policy network to select an action based on the current state using an epsilon-greedy strategy.\n", "\n", "    Args:\n", "        state (dict): The current state of the environment, including query, context, responses, and rewards.\n", "        action_space (List[str]): The list of possible actions the agent can take.\n", "        epsilon (float): The probability of choosing a random action for exploration. Default is 0.2.\n", "\n", "    Returns:\n", "        str: The selected action from the action space.\n", "    \"\"\"\n", "    # Use epsilon-greedy strategy: random exploration vs. exploitation\n", "    if np.random.random() < epsilon:\n", "        # Exploration: randomly select an action from the action space\n", "        action = np.random.choice(action_space)\n", "    else:\n", "        # Exploitation: select the best action based on the current state using a simple heuristic\n", "\n", "        # If there are no previous responses, prioritize rewriting the query\n", "        if len(state[\"previous_responses\"]) == 0:\n", "            action = \"rewrite_query\"\n", "        # If there are previous responses but the rewards are low, try expanding the context\n", "        elif state[\"previous_rewards\"] and max(state[\"previous_rewards\"]) < 0.7:\n", "            action = \"expand_context\"\n", "        # If the context has too many chunks, try filtering the context\n", "        elif len(state[\"context\"]) > 5:\n", "            action = \"filter_context\"\n", "        # Otherwise, generate a response\n", "        else:\n", "            action = \"generate_response\"\n", "    \n", "    return action"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So our policy network works like this:\n", "- If there are no previous responses, prioritize rewriting the query.\n", "- If there are previous responses but the rewards are low, try expanding the context.\n", "- If the context has too many chunks, try filtering the context.\n", "- Otherwise, generate a response."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Single RL Step\n", "\n", "We have coded an important component of the RL pipeline. For any developer who has done any kind of training, there exists a training loop where each iteration is a single step in which the RL agent takes an action, rewards are calculated, states are updated, and so on. So, we need to code a single step of our training loop. Let's do that."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["# Function to perform a single RL step\n", "def rl_step(\n", "    state: dict, \n", "    action_space: List[str], \n", "    ground_truth: str\n", ") -> tuple[dict, str, float, str]:\n", "    \"\"\"\n", "    Perform a single RL step: select an action, execute it, and calculate the reward.\n", "\n", "    Args:\n", "        state (dict): The current state of the environment, including query, context, responses, and rewards.\n", "        action_space (List[str]): The list of possible actions the agent can take.\n", "        ground_truth (str): The expected correct answer to calculate the reward.\n", "\n", "    Returns:\n", "        tuple: A tuple containing:\n", "            - state (dict): The updated state after executing the action.\n", "            - action (str): The action selected by the policy network.\n", "            - reward (float): The reward received for the action.\n", "            - response (str): The response generated (if applicable).\n", "    \"\"\"\n", "    # Select an action using the policy network\n", "    action: str = policy_network(state, action_space)\n", "    response: str = None  # Initialize response as None\n", "    reward: float = 0  # Initialize reward as 0\n", "\n", "    # Execute the selected action\n", "    if action == \"rewrite_query\":\n", "        # Rewrite the query to improve retrieval\n", "        rewritten_query: str = rewrite_query(state[\"original_query\"], state[\"context\"])\n", "        state[\"current_query\"] = rewritten_query  # Update the current query in the state\n", "        # Retrieve new context based on the rewritten query\n", "        new_context: List[str] = retrieve_relevant_chunks(rewritten_query)\n", "        state[\"context\"] = new_context  # Update the context in the state\n", "\n", "    elif action == \"expand_context\":\n", "        # Expand the context by retrieving additional chunks\n", "        expanded_context: List[str] = expand_context(state[\"current_query\"], state[\"context\"])\n", "        state[\"context\"] = expanded_context  # Update the context in the state\n", "\n", "    elif action == \"filter_context\":\n", "        # Filter the context to keep only the most relevant chunks\n", "        filtered_context: List[str] = filter_context(state[\"current_query\"], state[\"context\"])\n", "        state[\"context\"] = filtered_context  # Update the context in the state\n", "\n", "    elif action == \"generate_response\":\n", "        # Construct a prompt using the current query and context\n", "        prompt: str = construct_prompt(state[\"current_query\"], state[\"context\"])\n", "        # Generate a response using the LLM\n", "        response: str = generate_response(prompt)\n", "        # Calculate the reward based on the similarity between the response and the ground truth\n", "        reward: float = calculate_reward(response, ground_truth)\n", "        # Update the state with the new response and reward\n", "        state[\"previous_responses\"].append(response)\n", "        state[\"previous_rewards\"].append(reward)\n", "\n", "    # Return the updated state, selected action, reward, and response\n", "    return state, action, reward, response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In our single step function, we first select an action using the policy network. The policy network uses an epsilon-greedy strategy to balance exploration and exploitation. If the random number is less than epsilon, we choose a random action from the action space for exploration. Otherwise, we select the best action based on the current state using a simple heuristic."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training Parameters and Policy Update\n", "\n", "We need to define some training parameters for our training loop and also define a function to update the policy based on the rewards received.\n", "\n", "Though the training parameters function is **optional**, it can be used for advanced implementations of the RL pipeline."]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# Function to initialize training parameters\n", "def initialize_training_params() -> Dict[str, Union[float, int]]:\n", "    \"\"\"\n", "    Initialize training parameters such as learning rate, number of episodes, and discount factor.\n", "\n", "    Returns:\n", "        Dict[str, Union[float, int]]: A dictionary containing the initialized training parameters.\n", "    \"\"\"\n", "    params = {\n", "        \"learning_rate\": 0.01,  # Learning rate for policy updates\n", "        \"num_episodes\": 100,   # Total number of training episodes\n", "        \"discount_factor\": 0.99  # Discount factor for future rewards\n", "    }\n", "    return params"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Similar to how our state changes after each step in the RL process, the policy also needs to be updated based on the rewards received. The update_policy function takes the current policy, state, action, reward, and learning rate as input and returns the updated policy."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# Function to update policy based on reward\n", "def update_policy(\n", "    policy: Dict[str, Dict[str, Union[float, str]]], \n", "    state: Dict[str, object], \n", "    action: str, \n", "    reward: float, \n", "    learning_rate: float\n", ") -> Dict[str, Dict[str, Union[float, str]]]:\n", "    \"\"\"\n", "    Update the policy based on the reward received.\n", "\n", "    Args:\n", "        policy (Dict[str, Dict[str, Union[float, str]]]): The current policy to be updated.\n", "        state (Dict[str, object]): The current state of the environment.\n", "        action (str): The action taken by the agent.\n", "        reward (float): The reward received for the action.\n", "        learning_rate (float): The learning rate for updating the policy.\n", "\n", "    Returns:\n", "        Dict[str, Dict[str, Union[float, str]]]: The updated policy.\n", "    \"\"\"\n", "    # Example: Simple policy update (to be replaced with a proper RL algorithm)\n", "    policy[state[\"query\"]] = {\n", "        \"action\": action,  # Store the action taken\n", "        \"reward\": reward   # Store the reward received\n", "    }\n", "    return policy"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the above `update_policy` logic, we store the action taken and the reward received for each query in the policy dictionary. In a more advanced RL algorithm, the policy update would involve more sophisticated methods such as policy gradients or Q-learning."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, we need to implement progress tracking logic to monitor the training process. This will help us understand how the model is learning and improving over time."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# Function to track training progress\n", "def track_progress(\n", "    episode: int, \n", "    reward: float, \n", "    rewards_history: List[float]\n", ") -> List[float]:\n", "    \"\"\"\n", "    Track the training progress by storing rewards for each episode.\n", "\n", "    Args:\n", "        episode (int): The current episode number.\n", "        reward (float): The reward received in the current episode.\n", "        rewards_history (List[float]): A list to store the rewards for all episodes.\n", "\n", "    Returns:\n", "        List[float]: The updated rewards history.\n", "    \"\"\"\n", "    # Append the current reward to the rewards history\n", "    rewards_history.append(reward)\n", "    \n", "    # Print progress every 10 episodes\n", "    print(f\"Episode {episode}: Reward = {reward}\")\n", "    \n", "    return rewards_history"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training Loop\n", "\n", "Now that we have coded every part of the training loop, we can put it all together in a single function that implements the training loop for the RL-enhanced RAG system."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# Function to implement the training loop\n", "def training_loop(\n", "    query_text: str, \n", "    ground_truth: str, \n", "    params: Optional[Dict[str, Union[float, int]]] = None\n", ") -> Tuple[Dict[str, Dict[str, Union[float, str]]], List[float], List[List[str]], Optional[str]]:\n", "    \"\"\"\n", "    Implement the training loop for RL-enhanced RAG.\n", "\n", "    Args:\n", "        query_text (str): The input query text for the RAG pipeline.\n", "        ground_truth (str): The expected correct answer for the query.\n", "        params (Optional[Dict[str, Union[float, int]]]): Training parameters such as learning rate, \n", "            number of episodes, and discount factor. If None, default parameters are initialized.\n", "\n", "    Returns:\n", "        Tuple: A tuple containing:\n", "            - policy (Dict[str, Dict[str, Union[float, str]]]): The updated policy after training.\n", "            - rewards_history (List[float]): A list of rewards received in each episode.\n", "            - actions_history (List[List[str]]): A list of actions taken in each episode.\n", "            - best_response (Optional[str]): The best response generated during training.\n", "    \"\"\"\n", "    # Initialize training parameters if not provided\n", "    if params is None:\n", "        params = initialize_training_params()\n", "    \n", "    # Initialize variables to track progress\n", "    rewards_history: List[float] = []  # List to store rewards for each episode\n", "    actions_history: List[List[str]] = []  # List to store actions taken in each episode\n", "    policy: Dict[str, Dict[str, Union[float, str]]] = {}  # Policy dictionary to store actions and rewards\n", "    action_space: List[str] = define_action_space()  # Define the action space\n", "    best_response: Optional[str] = None  # Variable to store the best response\n", "    best_reward: float = -1  # Initialize the best reward to a very low value\n", "    \n", "    # Get initial performance from the simple RAG pipeline for comparison\n", "    simple_response: str = basic_rag_pipeline(query_text)\n", "    simple_reward: float = calculate_reward(simple_response, ground_truth)\n", "    print(f\"Simple RAG reward: {simple_reward:.4f}\")\n", "\n", "    # Start the training loop\n", "    for episode in range(params[\"num_episodes\"]):\n", "        # Reset the environment with the same query\n", "        context_chunks: List[str] = retrieve_relevant_chunks(query_text)\n", "        state: Dict[str, object] = define_state(query_text, context_chunks)\n", "        episode_reward: float = 0  # Initialize the reward for the current episode\n", "        episode_actions: List[str] = []  # Initialize the list of actions for the current episode\n", "        \n", "        # Maximum number of steps per episode to prevent infinite loops\n", "        for step in range(10):\n", "            # Perform a single RL step\n", "            state, action, reward, response = rl_step(state, action_space, ground_truth)\n", "            episode_actions.append(action)  # Record the action taken\n", "            \n", "            # If a response is generated, end the episode\n", "            if response:\n", "                episode_reward = reward  # Update the episode reward\n", "                \n", "                # Track the best response and reward\n", "                if reward > best_reward:\n", "                    best_reward = reward\n", "                    best_response = response\n", "                \n", "                break  # Exit the loop as the episode ends\n", "        \n", "        # Update rewards and actions history\n", "        rewards_history.append(episode_reward)\n", "        actions_history.append(episode_actions)\n", "        \n", "        # Print progress every 5 episodes\n", "        if episode % 5 == 0:\n", "            print(f\"Episode {episode}: Reward = {episode_reward:.4f}, Actions = {episode_actions}\")\n", "    \n", "    # Compare the best RL-enhanced RAG reward with the simple RAG reward\n", "    improvement: float = best_reward - simple_reward\n", "    print(f\"\\nTraining completed:\")\n", "    print(f\"Simple RAG reward: {simple_reward:.4f}\")\n", "    print(f\"Best RL-enhanced RAG reward: {best_reward:.4f}\")\n", "    print(f\"Improvement: {improvement:.4f} ({improvement * 100:.2f}%)\")\n", "\n", "    return policy, rewards_history, actions_history, best_response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This function will take the input query text, the expected ground truth answer, and optionally some training parameters. It will return the updated policy, a list of rewards received in each episode, a list of actions taken in each episode, and the best response generated during training.\n", "\n", "In more detail, the `training_loop` function will:\n", "- Initialize training parameters if not provided.\n", "- Get the initial performance from the simple RAG pipeline for comparison.\n", "- Start the training loop for the specified number of episodes.\n", "- Perform a single RL step in each episode.\n", "- Update rewards and actions history for each episode.\n", "- Print progress every 5 episodes.\n", "- Compare the best RL-enhanced RAG reward with the simple RAG reward.\n", "- Return the updated policy, rewards history, actions history, and the best response generated during training."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Performance Comparison Logic\n", "\n", "Although we can manually compare the simple RAG pipeline with the RL-based RAG pipeline, a function can definitely help us in this regard. So, let's define a function to compare the performance of the simple RAG pipeline with the RL-enhanced RAG pipeline."]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# Function to compare Simple RAG vs RL-Enhanced RAG\n", "def compare_rag_approaches(query_text: str, ground_truth: str) -> Tuple[str, str, float, float]:\n", "    \"\"\"\n", "    Compare the outputs of simple RAG versus RL-enhanced RAG.\n", "\n", "    Args:\n", "        query_text (str): The input query text for the RAG pipeline.\n", "        ground_truth (str): The expected correct answer for the query.\n", "\n", "    Returns:\n", "        Tuple[str, str, float, float]: A tuple containing:\n", "            - simple_response (str): The response generated by the simple RAG pipeline.\n", "            - best_rl_response (str): The best response generated by the RL-enhanced RAG pipeline.\n", "            - simple_similarity (float): The similarity score of the simple RAG response to the ground truth.\n", "            - rl_similarity (float): The similarity score of the RL-enhanced RAG response to the ground truth.\n", "    \"\"\"\n", "    print(\"=\" * 80)\n", "    print(f\"Query: {query_text}\")\n", "    print(\"=\" * 80)\n", "    \n", "    # Step 1: Generate a response using the simple RAG pipeline\n", "    # The basic RAG pipeline retrieves relevant chunks and generates a response without reinforcement learning.\n", "    simple_response: str = basic_rag_pipeline(query_text)\n", "    # Calculate the similarity score between the simple RAG response and the ground truth.\n", "    simple_similarity: float = calculate_reward(simple_response, ground_truth)\n", "    \n", "    print(\"\\nSimple RAG Output:\")\n", "    print(\"-\" * 40)\n", "    print(simple_response)\n", "    print(f\"Similarity to ground truth: {simple_similarity:.4f}\")\n", "    \n", "    # Step 2: Train the RL-enhanced RAG model\n", "    print(\"\\nTraining RL-enhanced RAG model...\")\n", "    # Initialize training parameters (e.g., learning rate, number of episodes, discount factor).\n", "    params: Dict[str, float | int] = initialize_training_params()\n", "    # Set the number of episodes to a smaller value for demonstration purposes.\n", "    params[\"num_episodes\"] = 5\n", "    \n", "    # Run the training loop for the RL-enhanced RAG model.\n", "    # This loop trains the model to optimize its responses using reinforcement learning.\n", "    _, rewards_history, actions_history, best_rl_response = training_loop(\n", "        query_text, ground_truth, params\n", "    )\n", "    \n", "    # If no response was generated during training, generate one using the current query and context.\n", "    if best_rl_response is None:\n", "        # Retrieve relevant chunks for the query.\n", "        context_chunks: List[str] = retrieve_relevant_chunks(query_text)\n", "        # Construct a prompt using the query and retrieved context.\n", "        prompt: str = construct_prompt(query_text, context_chunks)\n", "        # Generate a response using the language model.\n", "        best_rl_response: str = generate_response(prompt)\n", "    \n", "    # Calculate the similarity score between the RL-enhanced RAG response and the ground truth.\n", "    rl_similarity: float = calculate_reward(best_rl_response, ground_truth)\n", "    \n", "    print(\"\\nRL-enhanced RAG Output:\")\n", "    print(\"-\" * 40)\n", "    print(best_rl_response)\n", "    print(f\"Similarity to ground truth: {rl_similarity:.4f}\")\n", "    \n", "    # Step 3: Evaluate and compare the results\n", "    # Calculate the improvement in similarity score achieved by the RL-enhanced RAG model.\n", "    improvement: float = rl_similarity - simple_similarity\n", "    \n", "    print(\"\\nEvaluation Results:\")\n", "    print(\"-\" * 40)\n", "    print(f\"Simple RAG similarity to ground truth: {simple_similarity:.4f}\")\n", "    print(f\"RL-enhanced RAG similarity to ground truth: {rl_similarity:.4f}\")\n", "    print(f\"Improvement: {improvement * 100:.2f}%\")\n", "    \n", "    # Step 4: Plot the reward history (if there are enough episodes and matplotlib is available)\n", "    if len(rewards_history) > 1:\n", "        try:\n", "            import matplotlib.pyplot as plt\n", "            # Create a plot to visualize the reward history during RL training.\n", "            plt.figure(figsize=(10, 6))\n", "            plt.plot(rewards_history)\n", "            plt.title('Reward History During RL Training')\n", "            plt.xlabel('Episode')\n", "            plt.ylabel('Re<PERSON>')\n", "            plt.grid(True)\n", "            plt.show()\n", "        except ImportError:\n", "            # If matplotlib is not available, print a message instead of plotting.\n", "            print(\"Mat<PERSON>lotlib not available for plotting rewards\")\n", "    \n", "    # Return the results: responses and similarity scores for both approaches.\n", "    return simple_response, best_rl_response, simple_similarity, rl_similarity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So our performance comparison logic is not very complicated but is based on 4 steps:\n", "1. Generate a response using the simple RAG pipeline.\n", "2. Train the RL-enhanced RAG model using the training loop.\n", "3. Evaluate and compare the results.\n", "4. Plot the reward history (if available)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation Framework (**Optional**)\n", "\n", "This step is optional but in case you want to evaluate all the eval queries in the validation data, you can use the following code.\n", "\n", "First, to check the relevance of the retrieved chunks and the ground truth, we need to have a function that evaluates the relevance of the retrieved chunks."]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["# Function to evaluate relevance of retrieved chunks\n", "def evaluate_relevance(retrieved_chunks: List[str], ground_truth_chunks: List[str]) -> float:\n", "    \"\"\"\n", "    Evaluate the relevance of retrieved chunks by comparing them to ground truth chunks.\n", "\n", "    Args:\n", "        retrieved_chunks (List[str]): A list of text chunks retrieved by the system.\n", "        ground_truth_chunks (List[str]): A list of ground truth text chunks for comparison.\n", "\n", "    Returns:\n", "        float: The average relevance score between the retrieved chunks and the ground truth chunks.\n", "    \"\"\"\n", "    relevance_scores: List[float] = []  # Initialize a list to store relevance scores\n", "\n", "    # Iterate through pairs of retrieved and ground truth chunks\n", "    for retrieved, ground_truth in zip(retrieved_chunks, ground_truth_chunks):\n", "        # Calculate the cosine similarity between the embeddings of the retrieved and ground truth chunks\n", "        relevance: float = cosine_similarity(\n", "            generate_embeddings([retrieved])[0],\n", "            generate_embeddings([ground_truth])[0]\n", "        )\n", "        # Append the relevance score to the list\n", "        relevance_scores.append(relevance)\n", "\n", "    # Return the average relevance score\n", "    return np.mean(relevance_scores)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To evaluate the accuracy of the generated responses, we can use the cosine similarity between the embeddings of the generated responses and the ground truth. So let's define a function to evaluate the accuracy of the responses based on this similarity metric."]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["# Function to evaluate the accuracy of generated responses\n", "def evaluate_accuracy(responses: List[str], ground_truth_responses: List[str]) -> float:\n", "    \"\"\"\n", "    Evaluate the accuracy of generated responses by comparing them to ground truth responses.\n", "\n", "    Args:\n", "        responses (List[str]): A list of generated responses to evaluate.\n", "        ground_truth_responses (List[str]): A list of ground truth responses to compare against.\n", "\n", "    Returns:\n", "        float: The average accuracy score, calculated as the mean cosine similarity \n", "               between the embeddings of the generated responses and the ground truth responses.\n", "    \"\"\"\n", "    accuracy_scores: List[float] = []  # Initialize a list to store accuracy scores\n", "\n", "    # Iterate through each pair of generated response and ground truth response\n", "    for response, ground_truth in zip(responses, ground_truth_responses):\n", "        # Calculate the cosine similarity between the embeddings of the response and ground truth\n", "        accuracy: float = cosine_similarity(\n", "            generate_embeddings([response])[0],\n", "            generate_embeddings([ground_truth])[0]\n", "        )\n", "        # Append the accuracy score to the list\n", "        accuracy_scores.append(accuracy)\n", "\n", "    # Return the mean of the accuracy scores\n", "    return np.mean(accuracy_scores)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We also need to measure the response quality and assign a relevant score for it to be used in the reinforcement learning process."]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["# Function to evaluate response quality\n", "def evaluate_response_quality(responses: List[str]) -> float:\n", "    \"\"\"\n", "    Evaluate the quality of responses using a heuristic or external model.\n", "\n", "    Args:\n", "        responses (List[str]): A list of generated responses to evaluate.\n", "\n", "    Returns:\n", "        float: The average quality score of the responses, ranging from 0 to 1.\n", "    \"\"\"\n", "    quality_scores: List[float] = []  # Initialize a list to store quality scores for each response\n", "\n", "    for response in responses:\n", "        # Example heuristic: Calculate a quality score based on response length\n", "        # Normalize the length by a maximum of 100 words and cap the score at 1.0\n", "        quality: float = len(response.split()) / 100\n", "        quality_scores.append(min(quality, 1.0))  # Append the capped quality score to the list\n", "\n", "    # Return the average quality score across all responses\n", "    return np.mean(quality_scores)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then we can evaluate the performance of the RL-enhanced RAG model on the validation dataset:"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["# Function to evaluate RAG performance\n", "def evaluate_rag_performance(\n", "    queries: List[str], \n", "    ground_truth_chunks: List[str], \n", "    ground_truth_responses: List[str]\n", ") -> Dict[str, float]:\n", "    \"\"\"\n", "    Evaluate the performance of the RAG pipeline using relevance, accuracy, and response quality metrics.\n", "\n", "    Args:\n", "        queries (List[str]): A list of query strings to evaluate.\n", "        ground_truth_chunks (List[str]): A list of ground truth text chunks corresponding to the queries.\n", "        ground_truth_responses (List[str]): A list of ground truth responses corresponding to the queries.\n", "\n", "    Returns:\n", "        Dict[str, float]: A dictionary containing the average relevance, accuracy, and quality scores.\n", "    \"\"\"\n", "    # Initialize lists to store scores for each metric\n", "    relevance_scores: List[float] = []\n", "    accuracy_scores: List[float] = []\n", "    quality_scores: List[float] = []\n", "\n", "    # Iterate through each query and its corresponding ground truth data\n", "    for query, ground_truth_chunk, ground_truth_response in zip(queries, ground_truth_chunks, ground_truth_responses):\n", "        # Retrieve relevant chunks for the query\n", "        retrieved_chunks: List[str] = retrieve_relevant_chunks(query)\n", "        \n", "        # Evaluate the relevance of the retrieved chunks compared to the ground truth chunk\n", "        relevance: float = evaluate_relevance(retrieved_chunks, [ground_truth_chunk])\n", "        relevance_scores.append(relevance)\n", "\n", "        # Generate a response using the basic RAG pipeline\n", "        response: str = basic_rag_pipeline(query)\n", "        \n", "        # Evaluate the accuracy of the generated response compared to the ground truth response\n", "        accuracy: float = evaluate_accuracy([response], [ground_truth_response])\n", "        accuracy_scores.append(accuracy)\n", "\n", "        # Evaluate the quality of the generated response\n", "        quality: float = evaluate_response_quality([response])\n", "        quality_scores.append(quality)\n", "\n", "    # Calculate the average scores for each metric\n", "    avg_relevance: float = np.mean(relevance_scores)\n", "    avg_accuracy: float = np.mean(accuracy_scores)\n", "    avg_quality: float = np.mean(quality_scores)\n", "\n", "    # Return the average scores as a dictionary\n", "    return {\n", "        \"average_relevance\": avg_relevance,\n", "        \"average_accuracy\": avg_accuracy,\n", "        \"average_quality\": avg_quality\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating (RL vs Simple) RAG\n", "\n", "Ah, the moment of truth! Let's evaluate the performance of the simple RAG pipeline against the RL-enhanced RAG pipeline on our factual query, where the simple RAG previously failed to provide the correct answer. Let's see if the RL-enhanced RAG pipeline can perform better."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's revisit our evaluation query and see what the simple RAG pipeline generates for it."]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Running the Retrieval-Augmented Generation (RAG) pipeline...\n", "📥 Query: What is the mathematical representation of a qubit in superposition?\n", "\n", "🤖 AI Response:\n", "--------------------------------------------------\n", "ψ  α0  β1\n", "--------------------------------------------------\n", "✅ Ground Truth Answer:\n", "--------------------------------------------------\n", "|ÏˆâŸ© = Î±|0âŸ© + Î²|1âŸ©, where Î± and Î² are complex numbers satisfying |Î±|Â² + |Î²|Â² = 1, representing the probability amplitudes for measuring the qubit in state |0âŸ© or |1âŸ© respectively.\n", "--------------------------------------------------\n"]}], "source": ["# Print a message to indicate the start of the RAG pipeline\n", "print(\"🔍 Running the Retrieval-Augmented Generation (RAG) pipeline...\")\n", "print(f\"📥 Query: {sample_query}\\n\")\n", "\n", "# Run the RAG pipeline and get the response\n", "response = basic_rag_pipeline(sample_query)\n", "\n", "# Print the response with better formatting\n", "print(\"🤖 AI Response:\")\n", "print(\"-\" * 50)\n", "print(response.strip())\n", "print(\"-\" * 50)\n", "\n", "# Print the ground truth answer for comparison\n", "print(\"✅ Ground Truth Answer:\")\n", "print(\"-\" * 50)\n", "print(expected_answer)\n", "print(\"-\" * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================================================================\n", "Query: What is the mathematical representation of a qubit in superposition?\n", "================================================================================\n", "\n", "Simple RAG Output:\n", "----------------------------------------\n", "ψ  α0  β1 \n", "\n", "Similarity to ground truth: 0.6726\n", "\n", "Training RL-enhanced RAG model...\n", "Simple RAG reward: 0.6772\n", "Episode 0: Reward = 0.0000, Actions = ['rewrite_query', 'rewrite_query', np.str_('rewrite_query'), 'rewrite_query', np.str_('rewrite_query'), 'rewrite_query', 'rewrite_query', 'rewrite_query', np.str_('expand_context'), 'rewrite_query']\n", "\n", "Training completed:\n", "Simple RAG reward: 0.6772\n", "Best RL-enhanced RAG reward: 0.8652\n", "Improvement: 0.1879 (18.79%)\n", "\n", "RL-enhanced RAG Output:\n", "----------------------------------------\n", "The mathematical representation of a qubit in superposition is: \n", "ψ = α0 + β1 \n", "\n", "Where:\n", "\n", "* α and β are complex numbers.\n", "* α² + β² = 1  \n", "\n", "\n", "Let me know if you would like a deeper explanation of any of these terms! \n", "\n", "Similarity to ground truth: 0.8652\n", "\n", "Evaluation Results:\n", "----------------------------------------\n", "Simple RAG similarity to ground truth: 0.6726\n", "RL-enhanced RAG similarity to ground truth: 0.8652\n", "Improvement: 19.26%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Compare the performance of the simple RAG pipeline and the RL-enhanced RAG pipeline\n", "# using the sample query and its expected answer.\n", "# The function returns:\n", "# - simple_response: The response generated by the simple RAG pipeline.\n", "# - rl_response: The best response generated by the RL-enhanced RAG pipeline.\n", "# - simple_sim: The similarity score of the simple RAG response to the ground truth.\n", "# - rl_sim: The similarity score of the RL-enhanced RAG response to the ground truth.\n", "simple_response, rl_response, simple_sim, rl_sim = compare_rag_approaches(sample_query, expected_answer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can clearly see that the response generated by the RL-enhanced RAG model is more accurate and relevant compared to the simple RAG pipeline. The improvement in similarity to the ground truth is evident, indicating that the RL-enhanced model has learned to generate better responses through training."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Saving the Comparison Results\n", "\n", "After implementing the RL algorithm, we can save the comparison results to check the performance of the RL implementation later."]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Results saved to rl_rag_results.json\n"]}], "source": ["# Save the results for later comparison\n", "results = {\n", "    \"query\": query_text,  # The input query text\n", "    \"ground_truth\": expected_answer,  # The expected correct answer for the query\n", "    \"simple_rag\": {\n", "        \"response\": simple_response,  # The response generated by the simple RAG pipeline\n", "        \"similarity\": float(simple_sim)  # The similarity score of the simple RAG response to the ground truth\n", "    },\n", "    \"rl_rag\": {\n", "        \"response\": rl_response,  # The response generated by the RL-enhanced RAG pipeline\n", "        \"similarity\": float(rl_sim)  # The similarity score of the RL-enhanced RAG response to the ground truth\n", "    },\n", "    \"improvement\": float(rl_sim - simple_sim)  # The improvement in similarity score achieved by RL-enhanced RAG\n", "}\n", "\n", "# Save the results to a JSON file for future reference\n", "with open('rl_rag_results.json', 'w') as f:\n", "    json.dump(results, f, indent=2)  # Write the results dictionary to the file with indentation for readability\n", "\n", "# Print a confirmation message to indicate that the results have been saved\n", "print(\"\\nResults saved to rl_rag_results.json\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## What can we conclude?\n", "\n", "- The performance of the simple RAG is lower compared to the RL-enhanced RAG on factual queries.\n", "- The RL-enhanced RAG achieved a 19.5% improvement in the similarity score within 5 episodes.\n", "- Further improvements can be achieved by:\n", "    - Training for more episodes.\n", "    - Tuning hyperparameters.\n", "- Time is a key constraint for training.\n", "- Parallel implementation of the RL algorithm can help reduce training time."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}