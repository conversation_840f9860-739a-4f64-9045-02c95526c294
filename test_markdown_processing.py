#!/usr/bin/env python3
"""
Test script to verify the Markdown processing functionality
from the updated semantic chunking notebook.
"""

import os
import re
import numpy as np

def read_markdown_file(markdown_path):
    """
    Reads and processes text from a Markdown file.

    Args:
    markdown_path (str): Path to the Markdown file.

    Returns:
    str: Processed text from the Markdown file.
    """
    try:
        # Open and read the Markdown file
        with open(markdown_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Basic preprocessing: remove excessive whitespace and normalize line breaks
        # Remove markdown headers symbols for cleaner text processing
        content = re.sub(r'^#{1,6}\s*', '', content, flags=re.MULTILINE)
        
        # Remove markdown links but keep the text
        content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)
        
        # Remove markdown emphasis markers
        content = re.sub(r'[*_]{1,2}([^*_]+)[*_]{1,2}', r'\1', content)
        
        # Normalize whitespace
        content = re.sub(r'\n\s*\n', '\n\n', content)  # Normalize paragraph breaks
        content = re.sub(r'[ \t]+', ' ', content)  # Normalize spaces and tabs
        
        return content.strip()
        
    except FileNotFoundError:
        raise FileNotFoundError(f"Markdown file not found: {markdown_path}")
    except Exception as e:
        raise Exception(f"Error reading markdown file: {str(e)}")

def split_markdown_into_sentences(text):
    """
    Splits Markdown text into meaningful sentences, handling various punctuation and structure.
    
    Args:
    text (str): Input text from Markdown file.
    
    Returns:
    List[str]: List of sentences.
    """
    # Split on sentence-ending punctuation followed by whitespace
    sentences = re.split(r'[.!?]+\s+', text)
    
    # Filter out very short sentences and empty strings
    sentences = [s.strip() for s in sentences if len(s.strip()) > 10]
    
    # Remove sentences that are just punctuation or special characters
    sentences = [s for s in sentences if re.search(r'[a-zA-Z]', s)]
    
    return sentences

def test_markdown_processing():
    """Test the markdown processing functionality."""
    
    # Test file path
    markdown_path = "data/aig-doc.md"
    
    if not os.path.exists(markdown_path):
        print(f"❌ Test file not found: {markdown_path}")
        return False
    
    try:
        # Test reading the markdown file
        print("🔍 Testing Markdown file reading...")
        extracted_text = read_markdown_file(markdown_path)
        print(f"✅ Successfully read {len(extracted_text)} characters from Markdown file")
        
        # Test sentence splitting
        print("\n🔍 Testing sentence splitting...")
        sentences = split_markdown_into_sentences(extracted_text)
        print(f"✅ Successfully split text into {len(sentences)} sentences")
        
        # Show sample sentences
        print("\n📝 Sample sentences:")
        for i, sentence in enumerate(sentences[:5]):
            print(f"  {i+1}. {sentence[:100]}{'...' if len(sentence) > 100 else ''}")
        
        # Test basic statistics
        print(f"\n📊 Statistics:")
        print(f"  - Total characters: {len(extracted_text):,}")
        print(f"  - Total sentences: {len(sentences):,}")
        print(f"  - Average sentence length: {len(extracted_text) / len(sentences):.1f} characters")
        
        # Show first 500 characters
        print(f"\n📄 First 500 characters of processed text:")
        print("-" * 60)
        print(extracted_text[:500])
        print("-" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Markdown Processing for Semantic Chunking")
    print("=" * 60)
    
    success = test_markdown_processing()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests passed! The notebook should work correctly with Markdown files.")
    else:
        print("❌ Tests failed. Please check the implementation.")
