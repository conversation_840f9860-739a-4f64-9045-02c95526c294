{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["## Introduction to Seman<PERSON> Chun<PERSON> with <PERSON><PERSON>\n", "Text chunking is an essential step in Retrieval-Augmented Generation (RAG), where large text bodies are divided into meaningful segments to improve retrieval accuracy.\n", "Unlike fixed-length chunking, semantic chunking splits text based on the content similarity between sentences.\n", "\n", "### Breakpoint Methods:\n", "- **Percentile**: Finds the Xth percentile of all similarity differences and splits chunks where the drop is greater than this value.\n", "- **Standard Deviation**: Splits where similarity drops more than X standard deviations below the mean.\n", "- **Interquartile Range (IQR)**: Uses the interquartile distance (Q3 - Q1) to determine split points.\n", "\n", "This notebook implements semantic chunking **using the percentile method** and evaluates its performance on **Markdown documentation**. We process the AIGuardian knowledge base to demonstrate how semantic chunking works with structured documentation."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "We begin by importing necessary libraries."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import re\n", "from openai import OpenAI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reading Text from a Markdown File\n", "To implement RAG, we first need a source of textual data. In this case, we read and process text from a Markdown file, which provides structured content that's ideal for semantic chunking."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully loaded Markdown file with 13574 characters\n", "\n", "First 500 characters:\n", "AIGuardian Consolidated Knowledge Base v2 (Business-Focused)\n", "Concise version focusing on main business logic and user-facing concepts\n", "\n", "Table of Contents\n", "\n", "1. Product Overview & Core Concepts\n", "- AIGuardian Platform Overview\n", "- Litmus - AI Testing Service\n", "- Sentinel - AI Guardrails Service\n", "- Key Benefits & Use Cases\n", "\n", "2. Getting Started & Onboarding\n", "- How to Get Started\n", "- Litmus Onboarding Process\n", "- Sentinel Onboarding Process\n", "- Common Integration Patterns\n", "\n", "3. API Usage & Integration\n", "- Sentinel API Ov\n"]}], "source": ["def read_markdown_file(markdown_path):\n", "    \"\"\"\n", "    Reads and processes text from a Markdown file.\n", "\n", "    Args:\n", "    markdown_path (str): Path to the Markdown file.\n", "\n", "    Returns:\n", "    str: Processed text from the Markdown file.\n", "    \"\"\"\n", "    try:\n", "        # Open and read the Markdown file\n", "        with open(markdown_path, 'r', encoding='utf-8') as file:\n", "            content = file.read()\n", "        \n", "        # Basic preprocessing: remove excessive whitespace and normalize line breaks\n", "        # Remove markdown headers symbols for cleaner text processing\n", "        content = re.sub(r'^#{1,6}\\s*', '', content, flags=re.MULTILINE)\n", "        \n", "        # Remove markdown links but keep the text\n", "        content = re.sub(r'\\[([^\\]]+)\\]\\([^\\)]+\\)', r'\\1', content)\n", "        \n", "        # Remove markdown emphasis markers\n", "        content = re.sub(r'[*_]{1,2}([^*_]+)[*_]{1,2}', r'\\1', content)\n", "        \n", "        # Normalize whitespace\n", "        content = re.sub(r'\\n\\s*\\n', '\\n\\n', content)  # Normalize paragraph breaks\n", "        content = re.sub(r'[ \\t]+', ' ', content)  # Normalize spaces and tabs\n", "        \n", "        return content.strip()\n", "        \n", "    except FileNotFoundError:\n", "        raise FileNotFoundError(f\"Markdown file not found: {markdown_path}\")\n", "    except Exception as e:\n", "        raise Exception(f\"Error reading markdown file: {str(e)}\")\n", "\n", "# Define the path to the Markdown file\n", "markdown_path = \"data/aig-doc-v2.md\"\n", "\n", "# Read and process text from the Markdown file\n", "extracted_text = read_markdown_file(markdown_path)\n", "\n", "# Print the first 500 characters of the extracted text\n", "print(f\"Successfully loaded Markdown file with {len(extracted_text)} characters\")\n", "print(\"\\nFirst 500 characters:\")\n", "print(extracted_text[:500])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the OpenAI API Client\n", "We initialize the OpenAI client to generate embeddings and responses."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Initialize the OpenAI client with the base URL and API key\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",\n", "    api_key=os.getenv(\"OPENAI_API_KEY\")  # Retrieve the API key from environment variables\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Sentence-Level Embeddings\n", "We split text into sentences and generate embeddings."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def get_embedding(text, model=\"BAAI/bge-en-icl\"):\n", "    \"\"\"\n", "    Creates an embedding for the given text using OpenAI.\n", "\n", "    Args:\n", "    text (str): Input text.\n", "    model (str): Embedding model name.\n", "\n", "    Returns:\n", "    np.n<PERSON>ray: The embedding vector.\n", "    \"\"\"\n", "    response = client.embeddings.create(model=model, input=text)\n", "    return np.array(response.data[0].embedding)\n", "\n", "def split_markdown_into_sentences(text):\n", "    \"\"\"\n", "    Splits Markdown text into meaningful sentences, handling various punctuation and structure.\n", "    \n", "    Args:\n", "    text (str): Input text from Markdown file.\n", "    \n", "    Returns:\n", "    List[str]: List of sentences.\n", "    \"\"\"\n", "    # Split on sentence-ending punctuation followed by whitespace\n", "    sentences = re.split(r'[.!?]+\\s+', text)\n", "    \n", "    # Filter out very short sentences and empty strings\n", "    sentences = [s.strip() for s in sentences if len(s.strip()) > 10]\n", "    \n", "    # Remove sentences that are just punctuation or special characters\n", "    sentences = [s for s in sentences if re.search(r'[a-zA-Z]', s)]\n", "    \n", "    return sentences\n", "\n", "# Split text into sentences using improved method for Markdown\n", "sentences = split_markdown_into_sentences(extracted_text)\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['AIGuardian Consolidated Knowledge Base v2 (Business-Focused)\\nConcise version focusing on main business logic and user-facing concepts\\n\\nTable of Contents\\n\\n1', 'Product Overview & Core Concepts\\n- AIGuardian Platform Overview\\n- Litmus - AI Testing Service\\n- Sentinel - AI Guardrails Service\\n- Key Benefits & Use Cases\\n\\n2', 'Getting Started & Onboarding\\n- How to Get Started\\n- Litmus Onboarding Process\\n- Sentinel Onboarding Process\\n- Common Integration Patterns\\n\\n3', 'API Usage & Integration\\n- Sentinel API Overview\\n- Common API Use Cases\\n- Integration Best Practices\\n\\n4', 'Authentication & Access\\n- Authentication Overview\\n- User Permissions & Roles\\n\\n5', 'Environments & Access\\n- Available Environments\\n- Playground Access\\n\\n6', \"Troubleshooting & Support\\n- Common Issues & Solutions\\n- Getting Help\\n\\n---\\n\\nContent Sections\\n\\nAIGuardian Platform Overview\\n\\nAIGuardian is Singapore's government AI safety and security platform consisting of two main services:\\n\\nLitmus - AI Testing Service\\n- Purpose: Comprehensive AI safety testing for government applications\\n- What it does: Automated testing of AI models against safety and security risks\\n- Key capability: Runs hundreds of curated test prompts to identify vulnerabilities\\n- Integration: Works with CI/CD pipelines for continuous testing\\n- Target users: Development teams, QA engineers, compliance teams\\n\\nSentinel - AI Guardrails Service \\n- Purpose: Real-time protection for AI applications in production\\n- What it does: Monitors and filters AI inputs/outputs for safety violations\\n- Key capability: Detects prompt injection, toxicity, PII leakage, and other risks\\n- Integration: API-based guardrails that integrate into existing applications\\n- Target users: Application developers, product teams, operations teams\\n\\nCommon Infrastructure\\n- User Access Management (UAM): Centralized authentication and tenant management\\n- Multi-tenant Architecture: Secure isolation between different government agencies\\n- Government Compliance: Meets Singapore government security and data protection standards\\n\\n---\\n\\nLitmus - AI Testing Service\\n\\nWhat is Litmus\", 'Litmus provides automated AI safety testing as a service, enabling development teams to perform frequent and seamless security testing for Generative AI applications', 'Key Benefits\\n- Automated Testing: No manual effort required for safety checks\\n- Comprehensive Coverage: Tests against wide range of safety scenarios\\n- CI/CD Integration: Seamlessly integrates into development workflows\\n- Government Standards: Aligned with National AI Group (NAIG) guidelines\\n- Risk Awareness: Provides near real-time awareness of AI application risks\\n\\nHow Litmus Works\\n1', 'Setup: Tenant configures Litmus with their application endpoints\\n2']\n", "37\n"]}], "source": ["print(sentences[:10])\n", "print(len(sentences))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated 37 sentence embeddings from 37 sentences.\n"]}], "source": ["# Generate embeddings for each sentence\n", "embeddings = [get_embedding(sentence) for sentence in sentences]\n", "\n", "print(f\"Generated {len(embeddings)} sentence embeddings from {len(sentences)} sentences.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculating Similarity Differences\n", "We compute cosine similarity between consecutive sentences."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def cosine_similarity(vec1, vec2):\n", "    \"\"\"\n", "    Computes cosine similarity between two vectors.\n", "\n", "    Args:\n", "    vec1 (np.n<PERSON><PERSON>): First vector.\n", "    vec2 (np.n<PERSON><PERSON>): Second vector.\n", "\n", "    Returns:\n", "    float: Cosine similarity.\n", "    \"\"\"\n", "    return np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))\n", "\n", "# Compute similarity between consecutive sentences\n", "similarities = [cosine_similarity(embeddings[i], embeddings[i + 1]) for i in range(len(embeddings) - 1)]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implementing Semantic Chunking\n", "We implement three different methods for finding breakpoints."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def compute_breakpoints(similarities, method=\"percentile\", threshold=90):\n", "    \"\"\"\n", "    Computes chunking breakpoints based on similarity drops.\n", "\n", "    Args:\n", "    similarities (List[float]): List of similarity scores between sentences.\n", "    method (str): 'percentile', 'standard_deviation', or 'interquartile'.\n", "    threshold (float): Threshold value (percentile for 'percentile', std devs for 'standard_deviation').\n", "\n", "    Returns:\n", "    List[int]: Indices where chunk splits should occur.\n", "    \"\"\"\n", "    # Determine the threshold value based on the selected method\n", "    if method == \"percentile\":\n", "        # Calculate the Xth percentile of the similarity scores\n", "        threshold_value = np.percentile(similarities, threshold)\n", "    elif method == \"standard_deviation\":\n", "        # Calculate the mean and standard deviation of the similarity scores\n", "        mean = np.mean(similarities)\n", "        std_dev = np.std(similarities)\n", "        # Set the threshold value to mean minus X standard deviations\n", "        threshold_value = mean - (threshold * std_dev)\n", "    elif method == \"interquartile\":\n", "        # Calculate the first and third quartiles (Q1 and Q3)\n", "        q1, q3 = np.percentile(similarities, [25, 75])\n", "        # Set the threshold value using the IQR rule for outliers\n", "        threshold_value = q1 - 1.5 * (q3 - q1)\n", "    else:\n", "        # Raise an error if an invalid method is provided\n", "        raise ValueError(\"Invalid method. Choose 'percentile', 'standard_deviation', or 'interquartile'.\")\n", "\n", "    # Identify indices where similarity drops below the threshold value\n", "    return [i for i, sim in enumerate(similarities) if sim < threshold_value]\n", "\n", "# Compute breakpoints using the percentile method with a threshold of 90\n", "breakpoints = compute_breakpoints(similarities, method=\"percentile\", threshold=90)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 1, 4, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35]\n"]}], "source": ["print(breakpoints)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Splitting Text into Semantic Chunks\n", "We split the text based on computed breakpoints."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of semantic chunks: 33\n", "\n", "First text chunk:\n", "AIGuardian Consolidated Knowledge Base v2 (Business-Focused)\n", "Concise version focusing on main business logic and user-facing concepts\n", "\n", "Table of Contents\n", "\n", "1.\n"]}], "source": ["def split_into_chunks(sentences, breakpoints):\n", "    \"\"\"\n", "    Splits sentences into semantic chunks.\n", "\n", "    Args:\n", "    sentences (List[str]): List of sentences.\n", "    breakpoints (List[int]): Indices where chunking should occur.\n", "\n", "    Returns:\n", "    List[str]: List of text chunks.\n", "    \"\"\"\n", "    chunks = []  # Initialize an empty list to store the chunks\n", "    start = 0  # Initialize the start index\n", "\n", "    # Iterate through each breakpoint to create chunks\n", "    for bp in breakpoints:\n", "        # Append the chunk of sentences from start to the current breakpoint\n", "        chunks.append(\". \".join(sentences[start:bp + 1]) + \".\")\n", "        start = bp + 1  # Update the start index to the next sentence after the breakpoint\n", "\n", "    # Append the remaining sentences as the last chunk\n", "    chunks.append(\". \".join(sentences[start:]))\n", "    return chunks  # Return the list of chunks\n", "\n", "# Create chunks using the split_into_chunks function\n", "text_chunks = split_into_chunks(sentences, breakpoints)\n", "\n", "# Print the number of chunks created\n", "print(f\"Number of semantic chunks: {len(text_chunks)}\")\n", "\n", "# Print the first chunk to verify the result\n", "print(\"\\nFirst text chunk:\")\n", "print(text_chunks[0])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Embeddings for Semantic Chunks\n", "We create embeddings for each chunk for later retrieval."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def create_embeddings(text_chunks):\n", "    \"\"\"\n", "    Creates embeddings for each text chunk.\n", "\n", "    Args:\n", "    text_chunks (List[str]): List of text chunks.\n", "\n", "    Returns:\n", "    List[np.ndarray]: List of embedding vectors.\n", "    \"\"\"\n", "    # Generate embeddings for each text chunk using the get_embedding function\n", "    return [get_embedding(chunk) for chunk in text_chunks]\n", "\n", "# Create chunk embeddings using the create_embeddings function\n", "chunk_embeddings = create_embeddings(text_chunks)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Performing Semantic Search\n", "We implement cosine similarity to retrieve the most relevant chunks."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def semantic_search(query, text_chunks, chunk_embeddings, k=5):\n", "    \"\"\"\n", "    Finds the most relevant text chunks for a query.\n", "\n", "    Args:\n", "    query (str): Search query.\n", "    text_chunks (List[str]): List of text chunks.\n", "    chunk_embeddings (List[np.ndarray]): List of chunk embeddings.\n", "    k (int): Number of top results to return.\n", "\n", "    Returns:\n", "    List[str]: Top-k relevant chunks.\n", "    \"\"\"\n", "    # Generate an embedding for the query\n", "    query_embedding = get_embedding(query)\n", "    \n", "    # Calculate cosine similarity between the query embedding and each chunk embedding\n", "    similarities = [cosine_similarity(query_embedding, emb) for emb in chunk_embeddings]\n", "    \n", "    # Get the indices of the top-k most similar chunks\n", "    top_indices = np.argsort(similarities)[-k:][::-1]\n", "    \n", "    # Return the top-k most relevant text chunks\n", "    return [text_chunks[i] for i in top_indices]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: As it seems my app is not compatible with Litmus, is it possible to extract more samples of the prompts Litmus uses so I can test them on my app too?\n", "\n", "============================================================\n", "\n", "Context 1:\n", "----------------------------------------\n", "Setup: Tenant configures Litmus with their application endpoints\n", "2. Testing: Litmus sends curated test prompts to the application\n", "3.\n", "============================================================\n", "\n", "Context 2:\n", "----------------------------------------\n", "Account Setup: Receive credentials and access to Litmus platform\n", "2.\n", "============================================================\n", "\n", "Context 3:\n", "----------------------------------------\n", "Test Suite Selection: Choose appropriate test suites for your use case\n", "4.\n", "============================================================\n"]}], "source": ["# Define a test query relevant to the AIGuardian documentation\n", "query = \"As it seems my app is not compatible with Litmus, is it possible to extract more samples of the prompts Litmus uses so I can test them on my app too?\"\n", "\n", "# Get top 3 relevant chunks for better context\n", "top_chunks = semantic_search(query, text_chunks, chunk_embeddings, k=3)\n", "\n", "# Print the query\n", "print(f\"Query: {query}\")\n", "print(\"\\n\" + \"=\"*60)\n", "\n", "# Print the top 3 most relevant text chunks\n", "for i, chunk in enumerate(top_chunks):\n", "    print(f\"\\nContext {i+1}:\")\n", "    print(\"-\" * 40)\n", "    print(chunk)\n", "    print(\"=\"*60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generating a Response Based on Retrieved Chunks"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# Define the system prompt for the AI assistant\n", "system_prompt = \"You are an AI assistant that strictly answers based on the given context. If the answer cannot be derived directly from the provided context, respond with: 'I do not have enough information to answer that.'\"\n", "\n", "def generate_response(system_prompt, user_message, model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Generates a response from the AI model based on the system prompt and user message.\n", "\n", "    Args:\n", "    system_prompt (str): The system prompt to guide the AI's behavior.\n", "    user_message (str): The user's message or query.\n", "    model (str): The model to be used for generating the response. Default is \"meta-llama/Llama-2-7B-chat-hf\".\n", "\n", "    Returns:\n", "    dict: The response from the AI model.\n", "    \"\"\"\n", "    response = client.chat.completions.create(\n", "        model=model,\n", "        temperature=0,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_message}\n", "        ]\n", "    )\n", "    return response\n", "\n", "# Create the user prompt based on the top chunks\n", "user_prompt = \"\\n\".join([f\"Context {i + 1}:\\n{chunk}\\n=====================================\\n\" for i, chunk in enumerate(top_chunks)])\n", "user_prompt = f\"{user_prompt}\\nQuestion: {query}\"\n", "\n", "# Generate AI response\n", "ai_response = generate_response(system_prompt, user_prompt)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I do not have enough information to answer that.\n"]}], "source": ["print(ai_response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating the AI Response\n", "We can evaluate the quality and relevance of the AI response based on the retrieved context from our Markdown documentation."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== EVALUATION RESULTS ===\n", "Score: 0.9/1.0\n", "\n", "**Accuracy**: The response accurately describes both Litmus and Sentinel services based on the provided context. All information presented is factually correct and well-sourced from the documentation.\n", "\n", "**Completeness**: The response comprehensively addresses the user's question by explaining both main services, their individual functions, and how they work together. It covers pre-deployment testing (Litmus) and post-deployment monitoring (Sentinel).\n", "\n", "**Clarity**: The response is well-organized with clear numbering, bold headings, and logical flow. The explanation of how the services complement each other adds valuable context.\n", "\n", "**Minor deduction**: The response could have included more specific technical details about the testing methodologies or monitoring capabilities, but overall it provides an excellent overview of AIGuardian's services.\n"]}], "source": ["# Define the system prompt for the evaluation system\n", "evaluate_system_prompt = \"You are an intelligent evaluation system tasked with assessing AI assistant responses. Evaluate the response based on: 1) Accuracy relative to the provided context, 2) Completeness of the answer, 3) Clarity and organization. Provide a score from 0-1 and explain your reasoning.\"\n", "\n", "# Create the evaluation prompt\n", "evaluation_prompt = f\"\"\"User Query: {query}\n", "\n", "AI Response:\n", "{ai_response.choices[0].message.content}\n", "\n", "Context provided to AI:\n", "{''.join([f'Context {i+1}: {chunk}\\n\\n' for i, chunk in enumerate(top_chunks)])}\n", "\n", "Please evaluate this response based on accuracy, completeness, and clarity.\"\"\"\n", "\n", "# Generate the evaluation response\n", "evaluation_response = generate_response(evaluate_system_prompt, evaluation_prompt)\n", "\n", "# Print the evaluation response\n", "print(\"=== EVALUATION RESULTS ===\")\n", "print(evaluation_response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}