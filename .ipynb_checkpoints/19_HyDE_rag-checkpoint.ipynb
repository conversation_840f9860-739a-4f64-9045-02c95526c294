{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["# Hypothetical Document Embedding (HyDE) for RAG\n", "\n", "In this notebook, I implement HyDE (Hypothetical Document Embedding) - an innovative retrieval technique that transforms user queries into hypothetical answer documents before performing retrieval. This approach bridges the semantic gap between short queries and lengthy documents.\n", "\n", "Traditional RAG systems embed the user's short query directly, but this often fails to capture the semantic richness needed for optimal retrieval. HyDE solves this by:\n", "\n", "- Generating a hypothetical document that answers the query\n", "- Embedding this expanded document instead of the original query\n", "- Retrieving documents similar to this hypothetical document\n", "- Creating more contextually relevant answers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "We begin by importing necessary libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import json\n", "import fitz\n", "from openai import OpenAI\n", "import re\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the OpenAI API Client\n", "We initialize the OpenAI client to generate embeddings and responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the OpenAI client with the base URL and API key\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",\n", "    api_key=os.getenv(\"OPENAI_API_KEY\")  # Retrieve the API key from environment variables\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Processing Functions"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def extract_text_from_pdf(pdf_path):\n", "    \"\"\"\n", "    Extract text content from a PDF file with page separation.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        \n", "    Returns:\n", "        List[Dict]: List of pages with text content and metadata\n", "    \"\"\"\n", "    print(f\"Extracting text from {pdf_path}...\")  # Print the path of the PDF being processed\n", "    pdf = fitz.open(pdf_path)  # Open the PDF file using PyMuPDF\n", "    pages = []  # Initialize an empty list to store the pages with text content\n", "    \n", "    # Iterate over each page in the PDF\n", "    for page_num in range(len(pdf)):\n", "        page = pdf[page_num]  # Get the current page\n", "        text = page.get_text()  # Extract text from the current page\n", "        \n", "        # Skip pages with very little text (less than 50 characters)\n", "        if len(text.strip()) > 50:\n", "            # Append the page text and metadata to the list\n", "            pages.append({\n", "                \"text\": text,\n", "                \"metadata\": {\n", "                    \"source\": pdf_path,  # Source file path\n", "                    \"page\": page_num + 1  # Page number (1-based index)\n", "                }\n", "            })\n", "    \n", "    print(f\"Extracted {len(pages)} pages with content\")  # Print the number of pages extracted\n", "    return pages  # Return the list of pages with text content and metadata"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def chunk_text(text, chunk_size=1000, overlap=200):\n", "    \"\"\"\n", "    Split text into overlapping chunks.\n", "    \n", "    Args:\n", "        text (str): Input text to chunk\n", "        chunk_size (int): Size of each chunk in characters\n", "        overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        List[Dict]: List of chunks with metadata\n", "    \"\"\"\n", "    chunks = []  # Initialize an empty list to store the chunks\n", "    \n", "    # Iterate over the text in steps of (chunk_size - overlap)\n", "    for i in range(0, len(text), chunk_size - overlap):\n", "        chunk_text = text[i:i + chunk_size]  # Extract the chunk of text\n", "        if chunk_text:  # Ensure we don't add empty chunks\n", "            chunks.append({\n", "                \"text\": chunk_text,  # Add the chunk text\n", "                \"metadata\": {\n", "                    \"start_pos\": i,  # Start position of the chunk in the original text\n", "                    \"end_pos\": i + len(chunk_text)  # End position of the chunk in the original text\n", "                }\n", "            })\n", "    \n", "    print(f\"Created {len(chunks)} text chunks\")  # Print the number of chunks created\n", "    return chunks  # Return the list of chunks with metadata"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple Vector Store Implementation"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class SimpleVectorStore:\n", "    \"\"\"\n", "    A simple vector store implementation using NumPy.\n", "    \"\"\"\n", "    def __init__(self):\n", "        self.vectors = []  # List to store vector embeddings\n", "        self.texts = []  # List to store text content\n", "        self.metadata = []  # List to store metadata\n", "    \n", "    def add_item(self, text, embedding, metadata=None):\n", "        \"\"\"\n", "        Add an item to the vector store.\n", "        \n", "        Args:\n", "            text (str): Text content\n", "            embedding (List[float]): Vector embedding\n", "            metadata (Dict, optional): Additional metadata\n", "        \"\"\"\n", "        self.vectors.append(np.array(embedding))  # Append the embedding as a numpy array\n", "        self.texts.append(text)  # Append the text content\n", "        self.metadata.append(metadata or {})  # Append the metadata or an empty dict if None\n", "    \n", "    def similarity_search(self, query_embedding, k=5, filter_func=None):\n", "        \"\"\"\n", "        Find the most similar items to a query embedding.\n", "        \n", "        Args:\n", "            query_embedding (List[float]): Query embedding vector\n", "            k (int): Number of results to return\n", "            filter_func (callable, optional): Function to filter results\n", "            \n", "        Returns:\n", "            List[Dict]: Top k most similar items\n", "        \"\"\"\n", "        if not self.vectors:\n", "            return []  # Return an empty list if there are no vectors\n", "        \n", "        # Convert query embedding to numpy array\n", "        query_vector = np.array(query_embedding)\n", "        \n", "        # Calculate similarities using cosine similarity\n", "        similarities = []\n", "        for i, vector in enumerate(self.vectors):\n", "            # Skip if doesn't pass the filter\n", "            if filter_func and not filter_func(self.metadata[i]):\n", "                continue\n", "                \n", "            # Calculate cosine similarity\n", "            similarity = np.dot(query_vector, vector) / (np.linalg.norm(query_vector) * np.linalg.norm(vector))\n", "            similarities.append((i, similarity))  # Append index and similarity score\n", "        \n", "        # Sort by similarity (descending)\n", "        similarities.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        # Return top k results\n", "        results = []\n", "        for i in range(min(k, len(similarities))):\n", "            idx, score = similarities[i]\n", "            results.append({\n", "                \"text\": self.texts[idx],  # Add the text content\n", "                \"metadata\": self.metadata[idx],  # Add the metadata\n", "                \"similarity\": float(score)  # Add the similarity score\n", "            })\n", "        \n", "        return results  # Return the list of top k results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Embeddings"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def create_embeddings(texts, model=\"BAAI/bge-en-icl\"):\n", "    \"\"\"\n", "    Create embeddings for the given texts.\n", "    \n", "    Args:\n", "        texts (List[str]): Input texts\n", "        model (str): Embedding model name\n", "        \n", "    Returns:\n", "        List[List[float]]: Embedding vectors\n", "    \"\"\"\n", "    # Handle empty input\n", "    if not texts:\n", "        return []\n", "        \n", "    # Process in batches if needed (OpenAI API limits)\n", "    batch_size = 100\n", "    all_embeddings = []\n", "    \n", "    # Iterate over the input texts in batches\n", "    for i in range(0, len(texts), batch_size):\n", "        batch = texts[i:i + batch_size]  # Get the current batch of texts\n", "        \n", "        # Create embeddings for the current batch\n", "        response = client.embeddings.create(\n", "            model=model,\n", "            input=batch\n", "        )\n", "        \n", "        # Extract embeddings from the response\n", "        batch_embeddings = [item.embedding for item in response.data]\n", "        all_embeddings.extend(batch_embeddings)  # Add the batch embeddings to the list\n", "    \n", "    return all_embeddings  # Return all embeddings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Processing Pipeline"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def process_document(pdf_path, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Process a document for RAG.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        chunk_size (int): Size of each chunk in characters\n", "        chunk_overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        SimpleVectorStore: Vector store containing document chunks\n", "    \"\"\"\n", "    # Extract text from the PDF file\n", "    pages = extract_text_from_pdf(pdf_path)\n", "    \n", "    # Process each page and create chunks\n", "    all_chunks = []\n", "    for page in pages:\n", "        # Pass the text content (string) to chunk_text, not the dictionary\n", "        page_chunks = chunk_text(page[\"text\"], chunk_size, chunk_overlap)\n", "        \n", "        # Update metadata for each chunk with the page's metadata\n", "        for chunk in page_chunks:\n", "            chunk[\"metadata\"].update(page[\"metadata\"])\n", "        \n", "        all_chunks.extend(page_chunks)\n", "    \n", "    # Create embeddings for the text chunks\n", "    print(\"Creating embeddings for chunks...\")\n", "    chunk_texts = [chunk[\"text\"] for chunk in all_chunks]\n", "    chunk_embeddings = create_embeddings(chunk_texts)\n", "    \n", "    # Create a vector store to hold the chunks and their embeddings\n", "    vector_store = SimpleVectorStore()\n", "    for i, chunk in enumerate(all_chunks):\n", "        vector_store.add_item(\n", "            text=chunk[\"text\"],\n", "            embedding=chunk_embeddings[i],\n", "            metadata=chunk[\"metadata\"]\n", "        )\n", "    \n", "    print(f\"Vector store created with {len(all_chunks)} chunks\")\n", "    return vector_store"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Hypothetical Document Generation"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def generate_hypothetical_document(query, desired_length=1000):\n", "    \"\"\"\n", "    Generate a hypothetical document that answers the query.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        desired_length (int): Target length of the hypothetical document\n", "        \n", "    Returns:\n", "        str: Generated hypothetical document\n", "    \"\"\"\n", "    # Define the system prompt to instruct the model on how to generate the document\n", "    system_prompt = f\"\"\"You are an expert document creator. \n", "    Given a question, generate a detailed document that would directly answer this question.\n", "    The document should be approximately {desired_length} characters long and provide an in-depth, \n", "    informative answer to the question. Write as if this document is from an authoritative source\n", "    on the subject. Include specific details, facts, and explanations.\n", "    Do not mention that this is a hypothetical document - just write the content directly.\"\"\"\n", "\n", "    # Define the user prompt with the query\n", "    user_prompt = f\"Question: {query}\\n\\nGenerate a document that fully answers this question:\"\n", "    \n", "    # Make a request to the OpenAI API to generate the hypothetical document\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",  # Specify the model to use\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},  # System message to guide the assistant\n", "            {\"role\": \"user\", \"content\": user_prompt}  # User message with the query\n", "        ],\n", "        temperature=0.1  # Set the temperature for response generation\n", "    )\n", "    \n", "    # Return the generated document content\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complete HyDE RAG Implementation"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def hyde_rag(query, vector_store, k=5, should_generate_response=True):\n", "    \"\"\"\n", "    Perform RAG using Hypothetical Document Embedding.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        vector_store (SimpleVectorStore): Vector store with document chunks\n", "        k (int): Number of chunks to retrieve\n", "        generate_response (bool): Whether to generate a final response\n", "        \n", "    Returns:\n", "        Dict: Results including hypothetical document and retrieved chunks\n", "    \"\"\"\n", "    print(f\"\\n=== Processing query with HyDE: {query} ===\\n\")\n", "    \n", "    # Step 1: Generate a hypothetical document that answers the query\n", "    print(\"Generating hypothetical document...\")\n", "    hypothetical_doc = generate_hypothetical_document(query)\n", "    print(f\"Generated hypothetical document of {len(hypothetical_doc)} characters\")\n", "    \n", "    # Step 2: Create embedding for the hypothetical document\n", "    print(\"Creating embedding for hypothetical document...\")\n", "    hypothetical_embedding = create_embeddings([hypothetical_doc])[0]\n", "    \n", "    # Step 3: Retrieve similar chunks based on the hypothetical document\n", "    print(f\"Retrieving {k} most similar chunks...\")\n", "    retrieved_chunks = vector_store.similarity_search(hypothetical_embedding, k=k)\n", "    \n", "    # Prepare the results dictionary\n", "    results = {\n", "        \"query\": query,\n", "        \"hypothetical_document\": hypothetical_doc,\n", "        \"retrieved_chunks\": retrieved_chunks\n", "    }\n", "    \n", "    # Step 4: Generate a response if requested\n", "    if should_generate_response:\n", "        print(\"Generating final response...\")\n", "        response = generate_response(query, retrieved_chunks)\n", "        results[\"response\"] = response\n", "    \n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Standard (Direct) RAG Implementation for Comparison"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def standard_rag(query, vector_store, k=5, should_generate_response=True):\n", "    \"\"\"\n", "    Perform standard RAG using direct query embedding.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        vector_store (SimpleVectorStore): Vector store with document chunks\n", "        k (int): Number of chunks to retrieve\n", "        generate_response (bool): Whether to generate a final response\n", "        \n", "    Returns:\n", "        Dict: Results including retrieved chunks\n", "    \"\"\"\n", "    print(f\"\\n=== Processing query with Standard RAG: {query} ===\\n\")\n", "    \n", "    # Step 1: Create embedding for the query\n", "    print(\"Creating embedding for query...\")\n", "    query_embedding = create_embeddings([query])[0]\n", "    \n", "    # Step 2: Retrieve similar chunks based on the query embedding\n", "    print(f\"Retrieving {k} most similar chunks...\")\n", "    retrieved_chunks = vector_store.similarity_search(query_embedding, k=k)\n", "    \n", "    # Prepare the results dictionary\n", "    results = {\n", "        \"query\": query,\n", "        \"retrieved_chunks\": retrieved_chunks\n", "    }\n", "    \n", "    # Step 3: Generate a response if requested\n", "    if should_generate_response:\n", "        print(\"Generating final response...\")\n", "        response = generate_response(query, retrieved_chunks)\n", "        results[\"response\"] = response\n", "        \n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Response Generation"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def generate_response(query, relevant_chunks):\n", "    \"\"\"\n", "    Generate a final response based on the query and relevant chunks.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        relevant_chunks (List[Dict]): Retrieved relevant chunks\n", "        \n", "    Returns:\n", "        str: Generated response\n", "    \"\"\"\n", "    # Concatenate the text from the chunks to create context\n", "    context = \"\\n\\n\".join([chunk[\"text\"] for chunk in relevant_chunks])\n", "    \n", "    # Generate response using OpenAI API\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a helpful assistant. Answer the question based on the provided context.\"},\n", "            {\"role\": \"user\", \"content\": f\"Context:\\n{context}\\n\\nQuestion: {query}\"}\n", "        ],\n", "        temperature=0.5,\n", "        max_tokens=500\n", "    )\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation Functions"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def compare_approaches(query, vector_store, reference_answer=None):\n", "    \"\"\"\n", "    Compare HyDE and standard RAG approaches for a query.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        vector_store (SimpleVectorStore): Vector store with document chunks\n", "        reference_answer (str, optional): Reference answer for evaluation\n", "        \n", "    Returns:\n", "        Dict: Comparison results\n", "    \"\"\"\n", "    # Run HyDE RAG\n", "    hyde_result = hyde_rag(query, vector_store)\n", "    hyde_response = hyde_result[\"response\"]\n", "    \n", "    # Run standard RAG\n", "    standard_result = standard_rag(query, vector_store)\n", "    standard_response = standard_result[\"response\"]\n", "    \n", "    # Compare results\n", "    comparison = compare_responses(query, hyde_response, standard_response, reference_answer)\n", "    \n", "    return {\n", "        \"query\": query,\n", "        \"hyde_response\": hyde_response,\n", "        \"hyde_hypothetical_doc\": hyde_result[\"hypothetical_document\"],\n", "        \"standard_response\": standard_response,\n", "        \"reference_answer\": reference_answer,\n", "        \"comparison\": comparison\n", "    }"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["\n", "def compare_responses(query, hyde_response, standard_response, reference=None):\n", "    \"\"\"\n", "    Compare responses from HyDE and standard RAG.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        hyde_response (str): Response from HyDE RAG\n", "        standard_response (str): Response from standard RAG\n", "        reference (str, optional): Reference answer\n", "        \n", "    Returns:\n", "        str: Comparison analysis\n", "    \"\"\"\n", "    system_prompt = \"\"\"You are an expert evaluator of information retrieval systems.\n", "Compare the two responses to the same query, one generated using HyDE (Hypothetical Document Embedding) \n", "and the other using standard RAG with direct query embedding.\n", "\n", "Evaluate them based on:\n", "1. Accuracy: Which response provides more factually correct information?\n", "2. Relevance: Which response better addresses the query?\n", "3. Completeness: Which response provides more thorough coverage of the topic?\n", "4. Clarity: Which response is better organized and easier to understand?\n", "\n", "Be specific about the strengths and weaknesses of each approach.\"\"\"\n", "\n", "    user_prompt = f\"\"\"Query: {query}\n", "\n", "Response from HyDE RAG:\n", "{hyde_response}\n", "\n", "Response from Standard RAG:\n", "{standard_response}\"\"\"\n", "\n", "    if reference:\n", "        user_prompt += f\"\"\"\n", "\n", "Reference Answer:\n", "{reference}\"\"\"\n", "\n", "    user_prompt += \"\"\"\n", "\n", "Please provide a detailed comparison of these two responses, highlighting which approach performed better and why.\"\"\"\n", "\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    return response.choices[0].message.content\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def run_evaluation(pdf_path, test_queries, reference_answers=None, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Run a complete evaluation with multiple test queries.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF document\n", "        test_queries (List[str]): List of test queries\n", "        reference_answers (List[str], optional): Reference answers for queries\n", "        chunk_size (int): Size of each chunk in characters\n", "        chunk_overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        Dict: Evaluation results\n", "    \"\"\"\n", "    # Process document and create vector store\n", "    vector_store = process_document(pdf_path, chunk_size, chunk_overlap)\n", "    \n", "    results = []\n", "    \n", "    for i, query in enumerate(test_queries):\n", "        print(f\"\\n\\n===== Evaluating Query {i+1}/{len(test_queries)} =====\")\n", "        print(f\"Query: {query}\")\n", "        \n", "        # Get reference answer if available\n", "        reference = None\n", "        if reference_answers and i < len(reference_answers):\n", "            reference = reference_answers[i]\n", "        \n", "        # Compare approaches\n", "        result = compare_approaches(query, vector_store, reference)\n", "        results.append(result)\n", "    \n", "    # Generate overall analysis\n", "    overall_analysis = generate_overall_analysis(results)\n", "    \n", "    return {\n", "        \"results\": results,\n", "        \"overall_analysis\": overall_analysis\n", "    }"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def generate_overall_analysis(results):\n", "    \"\"\"\n", "    Generate an overall analysis of the evaluation results.\n", "    \n", "    Args:\n", "        results (List[Dict]): Results from individual query evaluations\n", "        \n", "    Returns:\n", "        str: Overall analysis\n", "    \"\"\"\n", "    system_prompt = \"\"\"You are an expert at evaluating information retrieval systems.\n", "Based on multiple test queries, provide an overall analysis comparing HyDE RAG (using hypothetical document embedding)\n", "with standard RAG (using direct query embedding).\n", "\n", "Focus on:\n", "1. When HyDE performs better and why\n", "2. When standard RAG performs better and why\n", "3. The types of queries that benefit most from HyDE\n", "4. The overall strengths and weaknesses of each approach\n", "5. Recommendations for when to use each approach\"\"\"\n", "\n", "    # Create summary of evaluations\n", "    evaluations_summary = \"\"\n", "    for i, result in enumerate(results):\n", "        evaluations_summary += f\"Query {i+1}: {result['query']}\\n\"\n", "        evaluations_summary += f\"Comparison summary: {result['comparison'][:200]}...\\n\\n\"\n", "\n", "    user_prompt = f\"\"\"Based on the following evaluations comparing HyDE vs standard RAG across {len(results)} queries, \n", "provide an overall analysis of these two approaches:\n", "\n", "{evaluations_summary}\n", "\n", "Please provide a comprehensive analysis of the relative strengths and weaknesses of HyDE compared to standard RAG,\n", "focusing on when and why one approach outperforms the other.\"\"\"\n", "\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization Functions"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def visualize_results(query, hyde_result, standard_result):\n", "    \"\"\"\n", "    Visualize the results of HyDE and standard RAG approaches.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        hyde_result (Dict): Results from HyDE RAG\n", "        standard_result (Dict): Results from standard RAG\n", "    \"\"\"\n", "    # Create a figure with 3 subplots\n", "    fig, axs = plt.subplots(1, 3, figsize=(20, 6))\n", "    \n", "    # Plot the query in the first subplot\n", "    axs[0].text(0.5, 0.5, f\"Query:\\n\\n{query}\", \n", "                horizontalalignment='center', verticalalignment='center',\n", "                fontsize=12, wrap=True)\n", "    axs[0].axis('off')  # Hide the axis for the query plot\n", "    \n", "    # Plot the hypothetical document in the second subplot\n", "    hypothetical_doc = hyde_result[\"hypothetical_document\"]\n", "    # Shorten the hypothetical document if it's too long\n", "    shortened_doc = hypothetical_doc[:500] + \"...\" if len(hypothetical_doc) > 500 else hypothetical_doc\n", "    axs[1].text(0.5, 0.5, f\"Hypothetical Document:\\n\\n{shortened_doc}\", \n", "                horizontalalignment='center', verticalalignment='center',\n", "                fontsize=10, wrap=True)\n", "    axs[1].axis('off')  # Hide the axis for the hypothetical document plot\n", "    \n", "    # Plot comparison of retrieved chunks in the third subplot\n", "    # Shorten each chunk text for better visualization\n", "    hyde_chunks = [chunk[\"text\"][:100] + \"...\" for chunk in hyde_result[\"retrieved_chunks\"]]\n", "    std_chunks = [chunk[\"text\"][:100] + \"...\" for chunk in standard_result[\"retrieved_chunks\"]]\n", "    \n", "    # Prepare the comparison text\n", "    comparison_text = \"Retrieved by HyDE:\\n\\n\"\n", "    for i, chunk in enumerate(hyde_chunks):\n", "        comparison_text += f\"{i+1}. {chunk}\\n\\n\"\n", "    \n", "    comparison_text += \"\\nRetrieved by Standard RAG:\\n\\n\"\n", "    for i, chunk in enumerate(std_chunks):\n", "        comparison_text += f\"{i+1}. {chunk}\\n\\n\"\n", "    \n", "    # Plot the comparison text in the third subplot\n", "    axs[2].text(0.5, 0.5, comparison_text, \n", "                horizontalalignment='center', verticalalignment='center',\n", "                fontsize=8, wrap=True)\n", "    axs[2].axis('off')  # Hide the axis for the comparison plot\n", "    \n", "    # Adjust layout to prevent overlap\n", "    plt.tight_layout()\n", "    # Display the plot\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation of Hypothetical Document Embedding (HyDE) vs. Standard RAG"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting text from data/AI_Information.pdf...\n", "Extracted 15 pages with content\n", "Created 4 text chunks\n", "Created 4 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 4 text chunks\n", "Created 3 text chunks\n", "Creating embeddings for chunks...\n", "Vector store created with 48 chunks\n", "\n", "=== Processing query with HyDE: What are the main ethical considerations in artificial intelligence development? ===\n", "\n", "Generating hypothetical document...\n", "Generated hypothetical document of 3364 characters\n", "Creating embedding for hypothetical document...\n", "Retrieving 5 most similar chunks...\n", "Generating final response...\n", "\n", "=== HyDE Response ===\n", "The main ethical considerations in artificial intelligence development include:\n", "\n", "1. Bias and Fairness: Ensuring that AI systems are fair, non-discriminatory, and do not perpetuate existing biases present in the data they are trained on.\n", "\n", "2. Transparency and Explainability: Making AI decisions more understandable and assessable to build trust and accountability.\n", "\n", "3. Privacy and Data Protection: Ensuring responsible data handling, implementing privacy-preserving techniques, and complying with data protection regulations.\n", "\n", "4. Accountability and Responsibility: Establishing clear guidelines and frameworks for AI development and deployment to address potential harms and ensure ethical behavior.\n", "\n", "5. Job Displacement: Addressing the potential economic and social impacts of AI-driven automation.\n", "\n", "6. Autonomy and Control: Establishing guidelines and frameworks to ensure that AI systems are developed and deployed in a way that prioritizes human well-being and safety.\n", "\n", "7. Weaponization of AI: Addressing the risks associated with the potential use of AI in autonomous weapons systems.\n", "\n", "8. Respect for Human Rights: Prioritizing the respect and protection of human rights, particularly in the development and deployment of AI systems.\n", "\n", "These considerations are guided by principles of Ethical AI, which include respect for human rights, privacy, non-discrimination, and beneficence.\n", "\n", "=== Processing query with Standard RAG: What are the main ethical considerations in artificial intelligence development? ===\n", "\n", "Creating embedding for query...\n", "Retrieving 5 most similar chunks...\n", "Generating final response...\n", "\n", "=== Standard RAG Response ===\n", "The main ethical considerations in artificial intelligence development include:\n", "\n", "1. Bias and Fairness: Ensuring that AI systems do not inherit and amplify biases present in the data they are trained on, leading to unfair or discriminatory outcomes.\n", "2. Transparency and Explainability: Making it possible to understand how AI systems arrive at their decisions and ensuring that these decisions are transparent and accountable.\n", "3. Respect for Human Rights, Privacy, Non-Discrimination, and Beneficence: Guiding the development and deployment of AI systems to ensure they are fair, transparent, accountable, and beneficial to society.\n", "4. Job Displacement: Addressing the potential economic and social impacts of AI-driven automation, particularly in industries with repetitive or routine tasks.\n", "5. Autonomy and Control: Establishing clear guidelines and ethical frameworks for AI development and deployment, particularly as AI systems become more autonomous.\n", "6. Weaponization of AI: Addressing the risks associated with AI-powered autonomous weapons and establishing international discussions and regulations.\n", "7. Cybersecurity: Protecting sensitive information and ensuring responsible data handling, as well as using AI to detect and respond to threats, analyze network traffic, and identify vulnerabilities.\n", "\n", "These considerations are essential to ensure that AI development aligns with societal values and promotes the well-being of individuals and society as a whole.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 2000x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Extracting text from data/AI_Information.pdf...\n", "Extracted 15 pages with content\n", "Created 4 text chunks\n", "Created 4 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 3 text chunks\n", "Created 4 text chunks\n", "Created 3 text chunks\n", "Creating embeddings for chunks...\n", "Vector store created with 48 chunks\n", "\n", "\n", "===== Evaluating Query 1/1 =====\n", "Query: How does neural network architecture impact AI performance?\n", "\n", "=== Processing query with HyDE: How does neural network architecture impact AI performance? ===\n", "\n", "Generating hypothetical document...\n", "Generated hypothetical document of 3438 characters\n", "Creating embedding for hypothetical document...\n", "Retrieving 5 most similar chunks...\n", "Generating final response...\n", "\n", "=== Processing query with Standard RAG: How does neural network architecture impact AI performance? ===\n", "\n", "Creating embedding for query...\n", "Retrieving 5 most similar chunks...\n", "Generating final response...\n", "\n", "=== OVERALL ANALYSIS ===\n", "**Overall Analysis: HyDE RAG vs Standard RAG**\n", "\n", "Based on the evaluation of Query 1, we can observe that both HyDE RAG and standard RAG provide accurate information about the impact of neural network architecture on AI performance. However, a more in-depth analysis of the strengths and weaknesses of each approach reveals the following:\n", "\n", "**Strengths and Weaknesses of HyDE RAG:**\n", "\n", "Strengths:\n", "\n", "1. **Improved contextual understanding**: HyDE RAG's use of document embedding allows it to capture the context and relationships between different concepts, leading to more accurate and informative responses.\n", "2. **Better handling of complex queries**: HyDE RAG's ability to represent documents as vectors enables it to capture the nuances of complex queries, such as the impact of neural network architecture on AI performance.\n", "\n", "Weaknesses:\n", "\n", "1. **Higher computational requirements**: HyDE RAG's use of document embedding requires more computational resources, which can lead to slower response times.\n", "2. **Overfitting to training data**: HyDE RAG's reliance on document embedding can lead to overfitting to the training data, which can result in poor performance on unseen queries.\n", "\n", "**Strengths and Weaknesses of Standard RAG:**\n", "\n", "Strengths:\n", "\n", "1. **Faster response times**: Standard RAG's use of direct query embedding allows it to respond quickly, even to complex queries.\n", "2. **Robustness to overfitting**: Standard RAG's direct query embedding approach is less prone to overfitting, as it does not rely on document representation.\n", "\n", "Weaknesses:\n", "\n", "1. **Limited contextual understanding**: Standard RAG's direct query embedding approach can lead to limited contextual understanding, resulting in less accurate and informative responses.\n", "2. **Difficulty handling complex queries**: Standard RAG's direct query embedding approach can struggle to capture the nuances of complex queries, such as the impact of neural network architecture on AI performance.\n", "\n", "**When HyDE RAG Outperforms Standard RAG:**\n", "\n", "HyDE RAG is likely to outperform Standard RAG in the following scenarios:\n", "\n", "1. **Complex queries**: HyDE RAG's ability to capture the nuances of complex queries, such as the impact of neural network architecture on AI performance, makes it a better choice for these types of queries.\n", "2. **Contextual understanding**: HyDE RAG's improved contextual understanding, enabled by document embedding, makes it a better choice when the query requires a deep understanding of the context and relationships between different concepts.\n", "\n", "**When Standard RAG Outperforms HyDE RAG:**\n", "\n", "Standard RAG is likely to outperform HyDE RAG in the following scenarios:\n", "\n", "1. **Simple queries**: Standard RAG's direct query embedding approach can respond quickly and accurately to simple queries, making it a better choice for these types of queries.\n", "2. **Real-time applications**: Standard RAG's faster response times make it a better choice for real-time applications, such as search engines or chatbots.\n", "\n", "**Recommendations:**\n", "\n", "1. **Use HyDE RAG for complex queries**: When dealing with complex queries that require a deep understanding of the context and relationships between different concepts, HyDE RAG is a better choice.\n", "2. **Use Standard RAG for simple queries**: When dealing with simple queries that require a quick and accurate response, Standard RAG is a better choice.\n", "3. **Use HyDE RAG for applications requiring contextual understanding**: When the application requires a deep understanding of the context and relationships between different concepts, HyDE RAG is a better choice.\n", "4. **Use Standard RAG for applications requiring real-time responses**: When the application requires a quick and accurate response, Standard RAG is a better choice.\n"]}], "source": ["# Path to the AI information document\n", "pdf_path = \"data/AI_Information.pdf\"\n", "\n", "# Process document and create vector store\n", "# This loads the document, extracts text, chunks it, and creates embeddings\n", "vector_store = process_document(pdf_path)\n", "\n", "# Example 1: Direct comparison for a single query related to AI\n", "query = \"What are the main ethical considerations in artificial intelligence development?\"\n", "\n", "# Run HyDE RAG approach\n", "# This generates a hypothetical document answering the query, embeds it, \n", "# and uses that embedding to retrieve relevant chunks\n", "hyde_result = hyde_rag(query, vector_store)\n", "print(\"\\n=== HyDE Response ===\")\n", "print(hyde_result[\"response\"])\n", "\n", "# Run standard RAG approach for comparison\n", "# This directly embeds the query and uses it to retrieve relevant chunks\n", "standard_result = standard_rag(query, vector_store)\n", "print(\"\\n=== Standard RAG Response ===\")\n", "print(standard_result[\"response\"])\n", "\n", "# Visualize the differences between HyDE and standard RAG approaches\n", "# Shows the query, hypothetical document, and retrieved chunks side by side\n", "visualize_results(query, hyde_result, standard_result)\n", "\n", "# Example 2: Run full evaluation with multiple AI-related queries\n", "test_queries = [\n", "    \"How does neural network architecture impact AI performance?\"\n", "]\n", "\n", "# Optional reference answers for better evaluation\n", "reference_answers = [\n", "    \"Neural network architecture significantly impacts AI performance through factors like depth (number of layers), width (neurons per layer), connectivity patterns, and activation functions. Different architectures like CNNs, RNNs, and Transformers are optimized for specific tasks such as image recognition, sequence processing, and natural language understanding respectively.\",\n", "]\n", "\n", "# Run comprehensive evaluation comparing HyDE and standard RAG approaches\n", "evaluation_results = run_evaluation(\n", "    pdf_path=pdf_path,\n", "    test_queries=test_queries,\n", "    reference_answers=reference_answers\n", ")\n", "\n", "# Print the overall analysis of which approach performs better across queries\n", "print(\"\\n=== OVERALL ANALYSIS ===\")\n", "print(evaluation_results[\"overall_analysis\"])"]}], "metadata": {"kernelspec": {"display_name": ".venv-new-specific-rag", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 2}