{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["# Self-RAG: A Dynamic Approach to RAG\n", "\n", "In this notebook, I implement Self-RAG, an advanced RAG system that dynamically decides when and how to use retrieved information. Unlike traditional RAG approaches, Self-RAG introduces reflection points throughout the retrieval and generation process, resulting in higher quality and more reliable responses.\n", "\n", "## Key Components of Self-RAG\n", "\n", "1. **Retrieval Decision**: Determines if retrieval is even necessary for a given query\n", "2. **Document Retrieval**: Fetches potentially relevant documents when needed  \n", "3. **Relevance Evaluation**: Assesses how relevant each retrieved document is\n", "4. **Response Generation**: Creates responses based on relevant contexts\n", "5. **Support Assessment**: Evaluates if responses are properly grounded in the context\n", "6. **Utility Evaluation**: Rates the overall usefulness of generated responses"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "We begin by importing necessary libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import json\n", "import fitz\n", "from openai import OpenAI\n", "import re"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extracting Text from a PDF File\n", "To implement RAG, we first need a source of textual data. In this case, we extract text from a PDF file using the PyMuPDF library."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def extract_text_from_pdf(pdf_path):\n", "    \"\"\"\n", "    Extracts text from a PDF file and prints the first `num_chars` characters.\n", "\n", "    Args:\n", "    pdf_path (str): Path to the PDF file.\n", "\n", "    Returns:\n", "    str: Extracted text from the PDF.\n", "    \"\"\"\n", "    # Open the PDF file\n", "    mypdf = fitz.open(pdf_path)\n", "    all_text = \"\"  # Initialize an empty string to store the extracted text\n", "\n", "    # Iterate through each page in the PDF\n", "    for page_num in range(mypdf.page_count):\n", "        page = mypdf[page_num]  # Get the page\n", "        text = page.get_text(\"text\")  # Extract text from the page\n", "        all_text += text  # Append the extracted text to the all_text string\n", "\n", "    return all_text  # Return the extracted text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chunking the Extracted Text\n", "Once we have the extracted text, we divide it into smaller, overlapping chunks to improve retrieval accuracy."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def chunk_text(text, n, overlap):\n", "    \"\"\"\n", "    Chunks the given text into segments of n characters with overlap.\n", "\n", "    Args:\n", "    text (str): The text to be chunked.\n", "    n (int): The number of characters in each chunk.\n", "    overlap (int): The number of overlapping characters between chunks.\n", "\n", "    Returns:\n", "    List[str]: A list of text chunks.\n", "    \"\"\"\n", "    chunks = []  # Initialize an empty list to store the chunks\n", "    \n", "    # Loop through the text with a step size of (n - overlap)\n", "    for i in range(0, len(text), n - overlap):\n", "        # Append a chunk of text from index i to i + n to the chunks list\n", "        chunks.append(text[i:i + n])\n", "\n", "    return chunks  # Return the list of text chunks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the OpenAI API Client\n", "We initialize the OpenAI client to generate embeddings and responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the OpenAI client with the base URL and API key\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",\n", "    api_key=os.getenv(\"OPENAI_API_KEY\")  # Retrieve the API key from environment variables\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simple Vector Store Implementation\n", "We'll create a basic vector store to manage document chunks and their embeddings."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class SimpleVectorStore:\n", "    \"\"\"\n", "    A simple vector store implementation using NumPy.\n", "    \"\"\"\n", "    def __init__(self):\n", "        \"\"\"\n", "        Initialize the vector store.\n", "        \"\"\"\n", "        self.vectors = []  # List to store embedding vectors\n", "        self.texts = []  # List to store original texts\n", "        self.metadata = []  # List to store metadata for each text\n", "    \n", "    def add_item(self, text, embedding, metadata=None):\n", "        \"\"\"\n", "        Add an item to the vector store.\n", "\n", "        Args:\n", "        text (str): The original text.\n", "        embedding (List[float]): The embedding vector.\n", "        metadata (dict, optional): Additional metadata.\n", "        \"\"\"\n", "        self.vectors.append(np.array(embedding))  # Convert embedding to numpy array and add to vectors list\n", "        self.texts.append(text)  # Add the original text to texts list\n", "        self.metadata.append(metadata or {})  # Add metadata to metadata list, default to empty dict if None\n", "    \n", "    def similarity_search(self, query_embedding, k=5, filter_func=None):\n", "        \"\"\"\n", "        Find the most similar items to a query embedding.\n", "\n", "        Args:\n", "        query_embedding (List[float]): Query embedding vector.\n", "        k (int): Number of results to return.\n", "        filter_func (callable, optional): Function to filter results.\n", "\n", "        Returns:\n", "        List[Dict]: Top k most similar items with their texts and metadata.\n", "        \"\"\"\n", "        if not self.vectors:\n", "            return []  # Return empty list if no vectors are stored\n", "        \n", "        # Convert query embedding to numpy array\n", "        query_vector = np.array(query_embedding)\n", "        \n", "        # Calculate similarities using cosine similarity\n", "        similarities = []\n", "        for i, vector in enumerate(self.vectors):\n", "            # Apply filter if provided\n", "            if filter_func and not filter_func(self.metadata[i]):\n", "                continue\n", "                \n", "            # Calculate cosine similarity\n", "            similarity = np.dot(query_vector, vector) / (np.linalg.norm(query_vector) * np.linalg.norm(vector))\n", "            similarities.append((i, similarity))  # Append index and similarity score\n", "        \n", "        # Sort by similarity (descending)\n", "        similarities.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        # Return top k results\n", "        results = []\n", "        for i in range(min(k, len(similarities))):\n", "            idx, score = similarities[i]\n", "            results.append({\n", "                \"text\": self.texts[idx],  # Add the text\n", "                \"metadata\": self.metadata[idx],  # Add the metadata\n", "                \"similarity\": score  # Add the similarity score\n", "            })\n", "        \n", "        return results  # Return the list of top k results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Embeddings"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def create_embeddings(text, model=\"BAAI/bge-en-icl\"):\n", "    \"\"\"\n", "    Creates embeddings for the given text.\n", "\n", "    Args:\n", "    text (str or List[str]): The input text(s) for which embeddings are to be created.\n", "    model (str): The model to be used for creating embeddings.\n", "\n", "    Returns:\n", "    List[float] or List[List[float]]: The embedding vector(s).\n", "    \"\"\"\n", "    # Handle both string and list inputs by converting string input to a list\n", "    input_text = text if isinstance(text, list) else [text]\n", "    \n", "    # Create embeddings for the input text using the specified model\n", "    response = client.embeddings.create(\n", "        model=model,\n", "        input=input_text\n", "    )\n", "    \n", "    # If the input was a single string, return just the first embedding\n", "    if isinstance(text, str):\n", "        return response.data[0].embedding\n", "    \n", "    # Otherwise, return all embeddings for the list of texts\n", "    return [item.embedding for item in response.data]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Processing Pipeline"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def process_document(pdf_path, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Process a document for Self-RAG.\n", "\n", "    Args:\n", "        pdf_path (str): Path to the PDF file.\n", "        chunk_size (int): Size of each chunk in characters.\n", "        chunk_overlap (int): Overlap between chunks in characters.\n", "\n", "    Returns:\n", "        SimpleVectorStore: A vector store containing document chunks and their embeddings.\n", "    \"\"\"\n", "    # Extract text from the PDF file\n", "    print(\"Extracting text from PDF...\")\n", "    extracted_text = extract_text_from_pdf(pdf_path)\n", "    \n", "    # Chunk the extracted text\n", "    print(\"Chunking text...\")\n", "    chunks = chunk_text(extracted_text, chunk_size, chunk_overlap)\n", "    print(f\"Created {len(chunks)} text chunks\")\n", "    \n", "    # Create embeddings for each chunk\n", "    print(\"Creating embeddings for chunks...\")\n", "    chunk_embeddings = create_embeddings(chunks)\n", "    \n", "    # Initialize the vector store\n", "    store = SimpleVectorStore()\n", "    \n", "    # Add each chunk and its embedding to the vector store\n", "    for i, (chunk, embedding) in enumerate(zip(chunks, chunk_embeddings)):\n", "        store.add_item(\n", "            text=chunk,\n", "            embedding=embedding,\n", "            metadata={\"index\": i, \"source\": pdf_path}\n", "        )\n", "    \n", "    print(f\"Added {len(chunks)} chunks to the vector store\")\n", "    return store"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Self-RAG Components\n", "### 1. Retrieval Decision"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def determine_if_retrieval_needed(query):\n", "    \"\"\"\n", "    Determines if retrieval is necessary for the given query.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        \n", "    Returns:\n", "        bool: True if retrieval is needed, False otherwise\n", "    \"\"\"\n", "    # System prompt to instruct the AI on how to determine if retrieval is necessary\n", "    system_prompt = \"\"\"You are an AI assistant that determines if retrieval is necessary to answer a query.\n", "    For factual questions, specific information requests, or questions about events, people, or concepts, answer \"Yes\".\n", "    For opinions, hypothetical scenarios, or simple queries with common knowledge, answer \"No\".\n", "    Answer with ONLY \"Yes\" or \"No\".\"\"\"\n", "\n", "    # User prompt containing the query\n", "    user_prompt = f\"Query: {query}\\n\\nIs retrieval necessary to answer this query accurately?\"\n", "    \n", "    # Generate response from the model\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    # Extract the answer from the model's response and convert to lowercase\n", "    answer = response.choices[0].message.content.strip().lower()\n", "    \n", "    # Return True if the answer contains \"yes\", otherwise return False\n", "    return \"yes\" in answer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Relevance Evaluation"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def evaluate_relevance(query, context):\n", "    \"\"\"\n", "    Evaluates the relevance of a context to the query.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        context (str): Context text\n", "        \n", "    Returns:\n", "        str: 'relevant' or 'irrelevant'\n", "    \"\"\"\n", "    # System prompt to instruct the AI on how to determine document relevance\n", "    system_prompt = \"\"\"You are an AI assistant that determines if a document is relevant to a query.\n", "    Consider whether the document contains information that would be helpful in answering the query.\n", "    Answer with ONLY \"Relevant\" or \"Irrelevant\".\"\"\"\n", "\n", "    # Truncate context if it is too long to avoid exceeding token limits\n", "    max_context_length = 2000\n", "    if len(context) > max_context_length:\n", "        context = context[:max_context_length] + \"... [truncated]\"\n", "\n", "    # User prompt containing the query and the document content\n", "    user_prompt = f\"\"\"Query: {query}\n", "    Document content:\n", "    {context}\n", "\n", "    Is this document relevant to the query? Answer with ONLY \"Relevant\" or \"Irrelevant\".\n", "    \"\"\"\n", "    \n", "    # Generate response from the model\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    # Extract the answer from the model's response and convert to lowercase\n", "    answer = response.choices[0].message.content.strip().lower()\n", "    \n", "    return answer  # Return the relevance evaluation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Support Assessment"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def assess_support(response, context):\n", "    \"\"\"\n", "    Assesses how well a response is supported by the context.\n", "    \n", "    Args:\n", "        response (str): Generated response\n", "        context (str): Context text\n", "        \n", "    Returns:\n", "        str: 'fully supported', 'partially supported', or 'no support'\n", "    \"\"\"\n", "    # System prompt to instruct the AI on how to evaluate support\n", "    system_prompt = \"\"\"You are an AI assistant that determines if a response is supported by the given context.\n", "    Evaluate if the facts, claims, and information in the response are backed by the context.\n", "    Answer with ONLY one of these three options:\n", "    - \"Fully supported\": All information in the response is directly supported by the context.\n", "    - \"Partially supported\": Some information in the response is supported by the context, but some is not.\n", "    - \"No support\": The response contains significant information not found in or contradicting the context.\n", "    \"\"\"\n", "\n", "    # Truncate context if it is too long to avoid exceeding token limits\n", "    max_context_length = 2000\n", "    if len(context) > max_context_length:\n", "        context = context[:max_context_length] + \"... [truncated]\"\n", "\n", "    # User prompt containing the context and the response to be evaluated\n", "    user_prompt = f\"\"\"Context:\n", "    {context}\n", "\n", "    Response:\n", "    {response}\n", "\n", "    How well is this response supported by the context? Answer with ONLY \"Fully supported\", \"Partially supported\", or \"No support\".\n", "    \"\"\"\n", "    \n", "    # Generate response from the model\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    # Extract the answer from the model's response and convert to lowercase\n", "    answer = response.choices[0].message.content.strip().lower()\n", "    \n", "    return answer  # Return the support assessment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. Utility Evaluation"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def rate_utility(query, response):\n", "    \"\"\"\n", "    Rates the utility of a response for the query.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        response (str): Generated response\n", "        \n", "    Returns:\n", "        int: Utility rating from 1 to 5\n", "    \"\"\"\n", "    # System prompt to instruct the AI on how to rate the utility of the response\n", "    system_prompt = \"\"\"You are an AI assistant that rates the utility of a response to a query.\n", "    Consider how well the response answers the query, its completeness, correctness, and helpfulness.\n", "    Rate the utility on a scale from 1 to 5, where:\n", "    - 1: Not useful at all\n", "    - 2: Slightly useful\n", "    - 3: Moderately useful\n", "    - 4: Very useful\n", "    - 5: Exceptionally useful\n", "    Answer with ONLY a single number from 1 to 5.\"\"\"\n", "\n", "    # User prompt containing the query and the response to be rated\n", "    user_prompt = f\"\"\"Query: {query}\n", "    Response:\n", "    {response}\n", "\n", "    Rate the utility of this response on a scale from 1 to 5:\"\"\"\n", "    \n", "    # Generate the utility rating using the OpenAI client\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    # Extract the rating from the model's response\n", "    rating = response.choices[0].message.content.strip()\n", "    \n", "    # Extract just the number from the rating\n", "    rating_match = re.search(r'[1-5]', rating)\n", "    if rating_match:\n", "        return int(rating_match.group())  # Return the extracted rating as an integer\n", "    \n", "    return 3  # De<PERSON><PERSON> to middle rating if parsing fails"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Response Generation"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def generate_response(query, context=None):\n", "    \"\"\"\n", "    Generates a response based on the query and optional context.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        context (str, optional): Context text\n", "        \n", "    Returns:\n", "        str: Generated response\n", "    \"\"\"\n", "    # System prompt to instruct the AI on how to generate a helpful response\n", "    system_prompt = \"\"\"You are a helpful AI assistant. Provide a clear, accurate, and informative response to the query.\"\"\"\n", "    \n", "    # Create the user prompt based on whether context is provided\n", "    if context:\n", "        user_prompt = f\"\"\"Context:\n", "        {context}\n", "\n", "        Query: {query}\n", "\n", "        Please answer the query based on the provided context.\n", "        \"\"\"\n", "    else:\n", "        user_prompt = f\"\"\"Query: {query}\n", "        \n", "        Please answer the query to the best of your ability.\"\"\"\n", "    \n", "    # Generate the response using the OpenAI client\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0.2\n", "    )\n", "    \n", "    # Return the generated response text\n", "    return response.choices[0].message.content.strip()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complete Self-RAG Implementation"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def self_rag(query, vector_store, top_k=3):\n", "    \"\"\"\n", "    Implements the complete Self-RAG pipeline.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        vector_store (SimpleVectorStore): Vector store containing document chunks\n", "        top_k (int): Number of documents to retrieve initially\n", "        \n", "    Returns:\n", "        dict: Results including query, response, and metrics from the Self-RAG process\n", "    \"\"\"\n", "    print(f\"\\n=== Starting Self-RAG for query: {query} ===\\n\")\n", "    \n", "    # Step 1: Determine if retrieval is necessary\n", "    print(\"Step 1: Determining if retrieval is necessary...\")\n", "    retrieval_needed = determine_if_retrieval_needed(query)\n", "    print(f\"Retrieval needed: {retrieval_needed}\")\n", "    \n", "    # Initialize metrics to track the Self-RAG process\n", "    metrics = {\n", "        \"retrieval_needed\": retrieval_needed,\n", "        \"documents_retrieved\": 0,\n", "        \"relevant_documents\": 0,\n", "        \"response_support_ratings\": [],\n", "        \"utility_ratings\": []\n", "    }\n", "    \n", "    best_response = None\n", "    best_score = -1\n", "    \n", "    if retrieval_needed:\n", "        # Step 2: Retrieve documents\n", "        print(\"\\nStep 2: Retrieving relevant documents...\")\n", "        query_embedding = create_embeddings(query)\n", "        results = vector_store.similarity_search(query_embedding, k=top_k)\n", "        metrics[\"documents_retrieved\"] = len(results)\n", "        print(f\"Retrieved {len(results)} documents\")\n", "        \n", "        # Step 3: Evaluate relevance of each document\n", "        print(\"\\nStep 3: Evaluating document relevance...\")\n", "        relevant_contexts = []\n", "        \n", "        for i, result in enumerate(results):\n", "            context = result[\"text\"]\n", "            relevance = evaluate_relevance(query, context)\n", "            print(f\"Document {i+1} relevance: {relevance}\")\n", "            \n", "            if relevance == \"relevant\":\n", "                relevant_contexts.append(context)\n", "        \n", "        metrics[\"relevant_documents\"] = len(relevant_contexts)\n", "        print(f\"Found {len(relevant_contexts)} relevant documents\")\n", "        \n", "        if relevant_contexts:\n", "            # Step 4: Process each relevant context\n", "            print(\"\\nStep 4: Processing relevant contexts...\")\n", "            for i, context in enumerate(relevant_contexts):\n", "                print(f\"\\nProcessing context {i+1}/{len(relevant_contexts)}...\")\n", "                \n", "                # Generate response based on the context\n", "                print(\"Generating response...\")\n", "                response = generate_response(query, context)\n", "                \n", "                # Assess how well the response is supported by the context\n", "                print(\"Assessing support...\")\n", "                support_rating = assess_support(response, context)\n", "                print(f\"Support rating: {support_rating}\")\n", "                metrics[\"response_support_ratings\"].append(support_rating)\n", "                \n", "                # Rate the utility of the response\n", "                print(\"Rating utility...\")\n", "                utility_rating = rate_utility(query, response)\n", "                print(f\"Utility rating: {utility_rating}/5\")\n", "                metrics[\"utility_ratings\"].append(utility_rating)\n", "                \n", "                # Calculate overall score (higher for better support and utility)\n", "                support_score = {\n", "                    \"fully supported\": 3, \n", "                    \"partially supported\": 1, \n", "                    \"no support\": 0\n", "                }.get(support_rating, 0)\n", "                \n", "                overall_score = support_score * 5 + utility_rating\n", "                print(f\"Overall score: {overall_score}\")\n", "                \n", "                # Keep track of the best response\n", "                if overall_score > best_score:\n", "                    best_response = response\n", "                    best_score = overall_score\n", "                    print(\"New best response found!\")\n", "        \n", "        # If no relevant contexts were found or all responses scored poorly\n", "        if not relevant_contexts or best_score <= 0:\n", "            print(\"\\nNo suitable context found or poor responses, generating without retrieval...\")\n", "            best_response = generate_response(query)\n", "    else:\n", "        # No retrieval needed, generate directly\n", "        print(\"\\nNo retrieval needed, generating response directly...\")\n", "        best_response = generate_response(query)\n", "    \n", "    # Final metrics\n", "    metrics[\"best_score\"] = best_score\n", "    metrics[\"used_retrieval\"] = retrieval_needed and best_score > 0\n", "    \n", "    print(\"\\n=== Self-RAG Completed ===\")\n", "    \n", "    return {\n", "        \"query\": query,\n", "        \"response\": best_response,\n", "        \"metrics\": metrics\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running the Complete Self-RAG System"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def run_self_rag_example():\n", "    \"\"\"\n", "    Demonstrates the complete Self-RAG system with examples.\n", "    \"\"\"\n", "    # Process document\n", "    pdf_path = \"data/AI_Information.pdf\"  # Path to the PDF document\n", "    print(f\"Processing document: {pdf_path}\")\n", "    vector_store = process_document(pdf_path)  # Process the document and create a vector store\n", "    \n", "    # Example 1: Query likely needing retrieval\n", "    query1 = \"What are the main ethical concerns in AI development?\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(f\"EXAMPLE 1: {query1}\")\n", "    result1 = self_rag(query1, vector_store)  # Run Self-RAG for the first query\n", "    print(\"\\nFinal response:\")\n", "    print(result1[\"response\"])  # Print the final response for the first query\n", "    print(\"\\nMetrics:\")\n", "    print(json.dumps(result1[\"metrics\"], indent=2))  # Print the metrics for the first query\n", "    \n", "    # Example 2: Query likely not needing retrieval\n", "    query2 = \"Can you write a short poem about artificial intelligence?\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(f\"EXAMPLE 2: {query2}\")\n", "    result2 = self_rag(query2, vector_store)  # Run Self-RAG for the second query\n", "    print(\"\\nFinal response:\")\n", "    print(result2[\"response\"])  # Print the final response for the second query\n", "    print(\"\\nMetrics:\")\n", "    print(json.dumps(result2[\"metrics\"], indent=2))  # Print the metrics for the second query\n", "    \n", "    # Example 3: Query with some relevance to document but requiring additional knowledge\n", "    query3 = \"How might AI impact economic growth in developing countries?\"\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(f\"EXAMPLE 3: {query3}\")\n", "    result3 = self_rag(query3, vector_store)  # Run Self-RAG for the third query\n", "    print(\"\\nFinal response:\")\n", "    print(result3[\"response\"])  # Print the final response for the third query\n", "    print(\"\\nMetrics:\")\n", "    print(json.dumps(result3[\"metrics\"], indent=2))  # Print the metrics for the third query\n", "    \n", "    return {\n", "        \"example1\": result1,\n", "        \"example2\": result2,\n", "        \"example3\": result3\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating Self-RAG Against Traditional RAG"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def traditional_rag(query, vector_store, top_k=3):\n", "    \"\"\"\n", "    Implements a traditional RAG approach for comparison.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        vector_store (SimpleVectorStore): Vector store containing document chunks\n", "        top_k (int): Number of documents to retrieve\n", "        \n", "    Returns:\n", "        str: Generated response\n", "    \"\"\"\n", "    print(f\"\\n=== Running traditional RAG for query: {query} ===\\n\")\n", "    \n", "    # Retrieve documents\n", "    print(\"Retrieving documents...\")\n", "    query_embedding = create_embeddings(query)  # Create embeddings for the query\n", "    results = vector_store.similarity_search(query_embedding, k=top_k)  # Search for similar documents\n", "    print(f\"Retrieved {len(results)} documents\")\n", "    \n", "    # Combine contexts from retrieved documents\n", "    contexts = [result[\"text\"] for result in results]  # Extract text from results\n", "    combined_context = \"\\n\\n\".join(contexts)  # Combine texts into a single context\n", "    \n", "    # Generate response using the combined context\n", "    print(\"Generating response...\")\n", "    response = generate_response(query, combined_context)  # Generate response based on the combined context\n", "    \n", "    return response"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def evaluate_rag_approaches(pdf_path, test_queries, reference_answers=None):\n", "    \"\"\"\n", "    Compare Self-RAG with traditional RAG.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the document\n", "        test_queries (List[str]): List of test queries\n", "        reference_answers (List[str], optional): Reference answers for evaluation\n", "        \n", "    Returns:\n", "        dict: Evaluation results\n", "    \"\"\"\n", "    print(\"=== Evaluating RAG Approaches ===\")\n", "    \n", "    # Process document to create a vector store\n", "    vector_store = process_document(pdf_path)\n", "    \n", "    results = []\n", "    \n", "    for i, query in enumerate(test_queries):\n", "        print(f\"\\nProcessing query {i+1}: {query}\")\n", "        \n", "        # Run Self-RAG\n", "        self_rag_result = self_rag(query, vector_store)  # Get response from Self-RAG\n", "        self_rag_response = self_rag_result[\"response\"]\n", "        \n", "        # Run traditional RAG\n", "        trad_rag_response = traditional_rag(query, vector_store)  # Get response from traditional RAG\n", "        \n", "        # Compare results if reference answer is available\n", "        reference = reference_answers[i] if reference_answers and i < len(reference_answers) else None\n", "        comparison = compare_responses(query, self_rag_response, trad_rag_response, reference)  # Compare responses\n", "        \n", "        results.append({\n", "            \"query\": query,\n", "            \"self_rag_response\": self_rag_response,\n", "            \"traditional_rag_response\": trad_rag_response,\n", "            \"reference_answer\": reference,\n", "            \"comparison\": comparison,\n", "            \"self_rag_metrics\": self_rag_result[\"metrics\"]\n", "        })\n", "    \n", "    # Generate overall analysis\n", "    overall_analysis = generate_overall_analysis(results)\n", "    \n", "    return {\n", "        \"results\": results,\n", "        \"overall_analysis\": overall_analysis\n", "    }"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def compare_responses(query, self_rag_response, trad_rag_response, reference=None):\n", "    \"\"\"\n", "    Compare responses from Self-RAG and traditional RAG.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        self_rag_response (str): Response from Self-RAG\n", "        trad_rag_response (str): Response from traditional RAG\n", "        reference (str, optional): Reference answer\n", "        \n", "    Returns:\n", "        str: Comparison analysis\n", "    \"\"\"\n", "    system_prompt = \"\"\"You are an expert evaluator of RAG systems. Your task is to compare responses from two different RAG approaches:\n", "1. Self-RAG: A dynamic approach that decides if retrieval is needed and evaluates information relevance and response quality\n", "2. Traditional RAG: Always retrieves documents and uses them to generate a response\n", "\n", "Compare the responses based on:\n", "- Relevance to the query\n", "- Factual correctness\n", "- Completeness and informativeness\n", "- Conciseness and focus\"\"\"\n", "\n", "    user_prompt = f\"\"\"Query: {query}\n", "\n", "Response from Self-RAG:\n", "{self_rag_response}\n", "\n", "Response from Traditional RAG:\n", "{trad_rag_response}\n", "\"\"\"\n", "\n", "    if reference:\n", "        user_prompt += f\"\"\"\n", "Reference Answer (for factual checking):\n", "{reference}\n", "\"\"\"\n", "\n", "    user_prompt += \"\"\"\n", "Compare these responses and explain which one is better and why.\n", "Focus on accuracy, relevance, completeness, and quality.\n", "\"\"\"\n", "\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",  # Using a stronger model for evaluation\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def generate_overall_analysis(results):\n", "    \"\"\"\n", "    Generate an overall analysis of Self-RAG vs traditional RAG.\n", "    \n", "    Args:\n", "        results (List[Dict]): Results from evaluate_rag_approaches\n", "        \n", "    Returns:\n", "        str: Overall analysis\n", "    \"\"\"\n", "    system_prompt = \"\"\"You are an expert evaluator of RAG systems. Your task is to provide an overall analysis comparing\n", "    Self-RAG and Traditional RAG based on multiple test queries.\n", "\n", "    Focus your analysis on:\n", "    1. When Self-RAG performs better and why\n", "    2. When Traditional RAG performs better and why\n", "    3. The impact of dynamic retrieval decisions in Self-RAG\n", "    4. The value of relevance and support evaluation in Self-RAG\n", "    5. Overall recommendations on which approach to use for different types of queries\"\"\"\n", "\n", "    # Prepare a summary of the individual comparisons\n", "    comparisons_summary = \"\"\n", "    for i, result in enumerate(results):\n", "        comparisons_summary += f\"Query {i+1}: {result['query']}\\n\"\n", "        comparisons_summary += f\"Self-RAG metrics: Retrieval needed: {result['self_rag_metrics']['retrieval_needed']}, \"\n", "        comparisons_summary += f\"Relevant docs: {result['self_rag_metrics']['relevant_documents']}/{result['self_rag_metrics']['documents_retrieved']}\\n\"\n", "        comparisons_summary += f\"Comparison summary: {result['comparison'][:200]}...\\n\\n\"\n", "\n", "        user_prompt = f\"\"\"Based on the following comparison results from {len(results)} test queries, please provide an overall analysis of\n", "    Self-RAG versus Traditional RAG:\n", "\n", "    {comparisons_summary}\n", "\n", "    Please provide your comprehensive analysis.\n", "    \"\"\"\n", "\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating the Self-RAG System\n", "\n", "The final step is to evaluate the Self-RAG system against traditional RAG approaches. We'll compare the quality of responses generated by both systems and analyze the performance of Self-RAG in different scenarios."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Evaluating RAG Approaches ===\n", "Extracting text from PDF...\n", "Chunking text...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 chunks to the vector store\n", "\n", "Processing query 1: What are the main ethical concerns in AI development?\n", "\n", "=== Starting Self-RAG for query: What are the main ethical concerns in AI development? ===\n", "\n", "Step 1: Determining if retrieval is necessary...\n", "Retrieval needed: True\n", "\n", "Step 2: Retrieving relevant documents...\n", "Retrieved 3 documents\n", "\n", "Step 3: Evaluating document relevance...\n", "Document 1 relevance: relevant\n", "Document 2 relevance: relevant\n", "Document 3 relevance: relevant\n", "Found 3 relevant documents\n", "\n", "Step 4: Processing relevant contexts...\n", "\n", "Processing context 1/3...\n", "Generating response...\n", "Assessing support...\n", "Support rating: fully supported\n", "Rating utility...\n", "Utility rating: 4/5\n", "Overall score: 19\n", "New best response found!\n", "\n", "Processing context 2/3...\n", "Generating response...\n", "Assessing support...\n", "Support rating: partially supported\n", "Rating utility...\n", "Utility rating: 4/5\n", "Overall score: 9\n", "\n", "Processing context 3/3...\n", "Generating response...\n", "Assessing support...\n", "Support rating: fully supported\n", "Rating utility...\n", "Utility rating: 5/5\n", "Overall score: 20\n", "New best response found!\n", "\n", "=== Self-RAG Completed ===\n", "\n", "=== Running traditional RAG for query: What are the main ethical concerns in AI development? ===\n", "\n", "Retrieving documents...\n", "Retrieved 3 documents\n", "Generating response...\n", "\n", "=== OVERALL ANALYSIS ===\n", "\n", "**Overall Analysis: Self-RAG vs Traditional RAG**\n", "\n", "Based on the comparison results from the test query \"What are the main ethical concerns in AI development?\", I will provide a comprehensive analysis of the strengths and weaknesses of both Self-RAG and Traditional RAG systems.\n", "\n", "**When Self-RAG performs better:**\n", "\n", "1. **Dynamic retrieval decisions**: Self-RAG's ability to dynamically adjust its retrieval decisions based on the query context and user feedback can lead to better results in complex queries with multiple relevant documents. In the case of Query 1, Self-RAG's retrieval needed was True, indicating that it was able to identify the most relevant documents for the query. This suggests that Self-RAG's dynamic retrieval decisions were effective in this scenario.\n", "2. **Relevance and support evaluation**: Self-RAG's evaluation of relevance and support can lead to more accurate and informative responses. In this case, Self-RAG's relevant docs were 3/3, indicating that it was able to identify the most relevant documents for the query. This suggests that Self-RAG's evaluation of relevance and support was effective in this scenario.\n", "\n", "**When Traditional RAG performs better:**\n", "\n", "1. **Simple queries**: Traditional RAG may perform better in simple queries with a single relevant document. In this case, the query \"What are the main ethical concerns in AI development?\" may have been too complex for Traditional RAG to handle effectively.\n", "2. **Pre-defined ranking**: Traditional RAG's pre-defined ranking may be more effective in scenarios where the ranking of documents is not critical. In this case, the query \"What are the main ethical concerns in AI development?\" may not have required a highly ranked response.\n", "\n", "**The impact of dynamic retrieval decisions in Self-RAG:**\n", "\n", "Self-RAG's dynamic retrieval decisions can lead to better results in complex queries with multiple relevant documents. However, this may also lead to over-retrieval or under-retrieval of documents, depending on the query context and user feedback. To mitigate this, Self-RAG's dynamic retrieval decisions should be carefully tuned to ensure that the most relevant documents are retrieved.\n", "\n", "**The value of relevance and support evaluation in Self-RAG:**\n", "\n", "Self-RAG's evaluation of relevance and support is critical in ensuring that the retrieved documents are accurate and informative. By evaluating the relevance and support of each document, Self-RAG can provide more accurate and informative responses. However, this evaluation should be carefully tuned to ensure that the most relevant documents are retrieved.\n", "\n", "**Overall recommendations:**\n", "\n", "1. **Use Self-RAG for complex queries**: Self-RAG's dynamic retrieval decisions and evaluation of relevance and support make it a better choice for complex queries with multiple relevant documents.\n", "2. **Use Traditional RAG for simple queries**: Traditional RAG's pre-defined ranking and simplicity make it a better choice for simple queries with a single relevant document.\n", "3. **<PERSON>ne Self-RAG's dynamic retrieval decisions**: Self-RAG's dynamic retrieval decisions should be carefully tuned to ensure that the most relevant documents are retrieved.\n", "4. **Evaluate relevance and support in Self-RAG**: Self-RAG's evaluation of relevance and support is critical in ensuring that the retrieved documents are accurate and informative.\n", "\n", "In conclusion, Self-RAG and Traditional RAG have different strengths and weaknesses, and the choice of which system to use depends on the type of query and the desired outcome. By understanding the strengths and weaknesses of each system, we can make informed decisions about which system to use in different scenarios.\n"]}], "source": ["# Path to the AI information document\n", "pdf_path = \"data/AI_Information.pdf\"\n", "\n", "# Define test queries covering different query types to test Self-RAG's adaptive retrieval\n", "test_queries = [\n", "    \"What are the main ethical concerns in AI development?\",        # Document-focused query\n", "    # \"How does explainable AI improve trust in AI systems?\",         # Document-focused query\n", "    # \"Write a poem about artificial intelligence\",                   # Creative query, doesn't need retrieval\n", "    # \"Will superintelligent AI lead to human obsolescence?\"          # Speculative query, partial retrieval needed\n", "]\n", "\n", "# Reference answers for more objective evaluation\n", "reference_answers = [\n", "    \"The main ethical concerns in AI development include bias and fairness, privacy, transparency, accountability, safety, and the potential for misuse or harmful applications.\",\n", "    # \"Explainable AI improves trust by making AI decision-making processes transparent and understandable to users, helping them verify fairness, identify potential biases, and better understand AI limitations.\",\n", "    # \"A quality poem about artificial intelligence should creatively explore themes of AI's capabilities, limitations, relationship with humanity, potential futures, or philosophical questions about consciousness and intelligence.\",\n", "    # \"Views on superintelligent AI's impact on human relevance vary widely. Some experts warn of potential risks if AI surpasses human capabilities across domains, possibly leading to economic displacement or loss of human agency. Others argue humans will remain relevant through complementary skills, emotional intelligence, and by defining AI's purpose. Most experts agree that thoughtful governance and human-centered design are essential regardless of the outcome.\"\n", "]\n", "\n", "# Run the evaluation comparing Self-RAG with traditional RAG approaches\n", "evaluation_results = evaluate_rag_approaches(\n", "    pdf_path=pdf_path,                  # Source document containing AI information\n", "    test_queries=test_queries,          # List of AI-related test queries\n", "    reference_answers=reference_answers  # Ground truth answers for evaluation\n", ")\n", "\n", "# Print the overall comparative analysis\n", "print(\"\\n=== OVERALL ANALYSIS ===\\n\")\n", "print(evaluation_results[\"overall_analysis\"])"]}], "metadata": {"kernelspec": {"display_name": ".venv-new-specific-rag", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 2}