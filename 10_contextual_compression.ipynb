{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["# Contextual Compression for Enhanced RAG Systems\n", "In this notebook, I implement a contextual compression technique to improve our RAG system's efficiency. We'll filter and compress retrieved text chunks to keep only the most relevant parts, reducing noise and improving response quality.\n", "\n", "When retrieving documents for RAG, we often get chunks containing both relevant and irrelevant information. Contextual compression helps us:\n", "\n", "- Remove irrelevant sentences and paragraphs\n", "- Focus only on query-relevant information\n", "- Maximize the useful signal in our context window\n", "\n", "Let's implement this approach from scratch!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "We begin by importing necessary libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import fitz\n", "import os\n", "import numpy as np\n", "import json\n", "from openai import OpenAI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extracting Text from a PDF File\n", "To implement RAG, we first need a source of textual data. In this case, we extract text from a PDF file using the PyMuPDF library."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def extract_text_from_pdf(pdf_path):\n", "    \"\"\"\n", "    Extracts text from a PDF file and prints the first `num_chars` characters.\n", "\n", "    Args:\n", "    pdf_path (str): Path to the PDF file.\n", "\n", "    Returns:\n", "    str: Extracted text from the PDF.\n", "    \"\"\"\n", "    # Open the PDF file\n", "    mypdf = fitz.open(pdf_path)\n", "    all_text = \"\"  # Initialize an empty string to store the extracted text\n", "\n", "    # Iterate through each page in the PDF\n", "    for page_num in range(mypdf.page_count):\n", "        page = mypdf[page_num]  # Get the page\n", "        text = page.get_text(\"text\")  # Extract text from the page\n", "        all_text += text  # Append the extracted text to the all_text string\n", "\n", "    return all_text  # Return the extracted text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chunking the Extracted Text\n", "Once we have the extracted text, we divide it into smaller, overlapping chunks to improve retrieval accuracy."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def chunk_text(text, n=1000, overlap=200):\n", "    \"\"\"\n", "    Chunks the given text into segments of n characters with overlap.\n", "\n", "    Args:\n", "    text (str): The text to be chunked.\n", "    n (int): The number of characters in each chunk.\n", "    overlap (int): The number of overlapping characters between chunks.\n", "\n", "    Returns:\n", "    List[str]: A list of text chunks.\n", "    \"\"\"\n", "    chunks = []  # Initialize an empty list to store the chunks\n", "    \n", "    # Loop through the text with a step size of (n - overlap)\n", "    for i in range(0, len(text), n - overlap):\n", "        # Append a chunk of text from index i to i + n to the chunks list\n", "        chunks.append(text[i:i + n])\n", "\n", "    return chunks  # Return the list of text chunks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the OpenAI API Client\n", "We initialize the OpenAI client to generate embeddings and responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the OpenAI client with the base URL and API key\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",\n", "    api_key= os.environ.get(\"OPENAI_API_KEY\") # Use your OpenAI API key\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building a Simple Vector Store\n", "let's implement a simple vector store since we cannot use FAISS."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class SimpleVectorStore:\n", "    \"\"\"\n", "    A simple vector store implementation using NumPy.\n", "    \"\"\"\n", "    def __init__(self):\n", "        \"\"\"\n", "        Initialize the vector store.\n", "        \"\"\"\n", "        self.vectors = []  # List to store embedding vectors\n", "        self.texts = []  # List to store original texts\n", "        self.metadata = []  # List to store metadata for each text\n", "    \n", "    def add_item(self, text, embedding, metadata=None):\n", "        \"\"\"\n", "        Add an item to the vector store.\n", "\n", "        Args:\n", "        text (str): The original text.\n", "        embedding (List[float]): The embedding vector.\n", "        metadata (dict, optional): Additional metadata.\n", "        \"\"\"\n", "        self.vectors.append(np.array(embedding))  # Convert embedding to numpy array and add to vectors list\n", "        self.texts.append(text)  # Add the original text to texts list\n", "        self.metadata.append(metadata or {})  # Add metadata to metadata list, use empty dict if None\n", "    \n", "    def similarity_search(self, query_embedding, k=5):\n", "        \"\"\"\n", "        Find the most similar items to a query embedding.\n", "\n", "        Args:\n", "        query_embedding (List[float]): Query embedding vector.\n", "        k (int): Number of results to return.\n", "\n", "        Returns:\n", "        List[Dict]: Top k most similar items with their texts and metadata.\n", "        \"\"\"\n", "        if not self.vectors:\n", "            return []  # Return empty list if no vectors are stored\n", "        \n", "        # Convert query embedding to numpy array\n", "        query_vector = np.array(query_embedding)\n", "        \n", "        # Calculate similarities using cosine similarity\n", "        similarities = []\n", "        for i, vector in enumerate(self.vectors):\n", "            similarity = np.dot(query_vector, vector) / (np.linalg.norm(query_vector) * np.linalg.norm(vector))\n", "            similarities.append((i, similarity))  # Append index and similarity score\n", "        \n", "        # Sort by similarity (descending)\n", "        similarities.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        # Return top k results\n", "        results = []\n", "        for i in range(min(k, len(similarities))):\n", "            idx, score = similarities[i]\n", "            results.append({\n", "                \"text\": self.texts[idx],  # Add the text corresponding to the index\n", "                \"metadata\": self.metadata[idx],  # Add the metadata corresponding to the index\n", "                \"similarity\": score  # Add the similarity score\n", "            })\n", "        \n", "        return results  # Return the list of top k results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Embedding Generation"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def create_embeddings(text,  model=\"BAAI/bge-en-icl\"):\n", "    \"\"\"\n", "    Creates embeddings for the given text.\n", "\n", "    Args:\n", "    text (str or List[str]): The input text(s) for which embeddings are to be created.\n", "    model (str): The model to be used for creating embeddings.\n", "\n", "    Returns:\n", "    List[float] or List[List[float]]: The embedding vector(s).\n", "    \"\"\"\n", "    # Handle both string and list inputs by ensuring input_text is always a list\n", "    input_text = text if isinstance(text, list) else [text]\n", "    \n", "    # Create embeddings for the input text using the specified model\n", "    response = client.embeddings.create(\n", "        model=model,\n", "        input=input_text\n", "    )\n", "    \n", "    # If the input was a single string, return just the first embedding\n", "    if isinstance(text, str):\n", "        return response.data[0].embedding\n", "    \n", "    # Otherwise, return all embeddings for the list of input texts\n", "    return [item.embedding for item in response.data]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building Our Document Processing Pipeline"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def process_document(pdf_path, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Process a document for RAG.\n", "\n", "    Args:\n", "    pdf_path (str): Path to the PDF file.\n", "    chunk_size (int): Size of each chunk in characters.\n", "    chunk_overlap (int): Overlap between chunks in characters.\n", "\n", "    Returns:\n", "    SimpleVectorStore: A vector store containing document chunks and their embeddings.\n", "    \"\"\"\n", "    # Extract text from the PDF file\n", "    print(\"Extracting text from PDF...\")\n", "    extracted_text = extract_text_from_pdf(pdf_path)\n", "    \n", "    # Chunk the extracted text into smaller segments\n", "    print(\"Chunking text...\")\n", "    chunks = chunk_text(extracted_text, chunk_size, chunk_overlap)\n", "    print(f\"Created {len(chunks)} text chunks\")\n", "    \n", "    # Create embeddings for each text chunk\n", "    print(\"Creating embeddings for chunks...\")\n", "    chunk_embeddings = create_embeddings(chunks)\n", "    \n", "    # Initialize a simple vector store to store the chunks and their embeddings\n", "    store = SimpleVectorStore()\n", "    \n", "    # Add each chunk and its corresponding embedding to the vector store\n", "    for i, (chunk, embedding) in enumerate(zip(chunks, chunk_embeddings)):\n", "        store.add_item(\n", "            text=chunk,\n", "            embedding=embedding,\n", "            metadata={\"index\": i, \"source\": pdf_path}\n", "        )\n", "    \n", "    print(f\"Added {len(chunks)} chunks to the vector store\")\n", "    return store"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implementing Contextual Compression\n", "This is the core of our approach - we'll use an LLM to filter and compress retrieved content."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def compress_chunk(chunk, query, compression_type=\"selective\", model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Compress a retrieved chunk by keeping only the parts relevant to the query.\n", "    \n", "    Args:\n", "        chunk (str): Text chunk to compress\n", "        query (str): User query\n", "        compression_type (str): Type of compression (\"selective\", \"summary\", or \"extraction\")\n", "        model (str): LLM model to use\n", "        \n", "    Returns:\n", "        str: Compressed chunk\n", "    \"\"\"\n", "    # Define system prompts for different compression approaches\n", "    if compression_type == \"selective\":\n", "        system_prompt = \"\"\"You are an expert at information filtering. \n", "        Your task is to analyze a document chunk and extract ONLY the sentences or paragraphs that are directly \n", "        relevant to the user's query. Remove all irrelevant content.\n", "\n", "        Your output should:\n", "        1. ONLY include text that helps answer the query\n", "        2. Preserve the exact wording of relevant sentences (do not paraphrase)\n", "        3. Maintain the original order of the text\n", "        4. Include ALL relevant content, even if it seems redundant\n", "        5. EXCLUDE any text that isn't relevant to the query\n", "\n", "        Format your response as plain text with no additional comments.\"\"\"\n", "    elif compression_type == \"summary\":\n", "        system_prompt = \"\"\"You are an expert at summarization. \n", "        Your task is to create a concise summary of the provided chunk that focuses ONLY on \n", "        information relevant to the user's query.\n", "\n", "        Your output should:\n", "        1. Be brief but comprehensive regarding query-relevant information\n", "        2. Focus exclusively on information related to the query\n", "        3. Omit irrelevant details\n", "        4. Be written in a neutral, factual tone\n", "\n", "        Format your response as plain text with no additional comments.\"\"\"\n", "    else:  # extraction\n", "        system_prompt = \"\"\"You are an expert at information extraction.\n", "        Your task is to extract ONLY the exact sentences from the document chunk that contain information relevant \n", "        to answering the user's query.\n", "\n", "        Your output should:\n", "        1. Include ONLY direct quotes of relevant sentences from the original text\n", "        2. Preserve the original wording (do not modify the text)\n", "        3. Include ONLY sentences that directly relate to the query\n", "        4. Separate extracted sentences with newlines\n", "        5. Do not add any commentary or additional text\n", "\n", "        Format your response as plain text with no additional comments.\"\"\"\n", "\n", "    # Define the user prompt with the query and document chunk\n", "    user_prompt = f\"\"\"\n", "        Query: {query}\n", "\n", "        Document Chunk:\n", "        {chunk}\n", "\n", "        Extract only the content relevant to answering this query.\n", "    \"\"\"\n", "    \n", "    # Generate a response using the OpenAI API\n", "    response = client.chat.completions.create(\n", "        model=model,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    # Extract the compressed chunk from the response\n", "    compressed_chunk = response.choices[0].message.content.strip()\n", "    \n", "    # Calculate compression ratio\n", "    original_length = len(chunk)\n", "    compressed_length = len(compressed_chunk)\n", "    compression_ratio = (original_length - compressed_length) / original_length * 100\n", "    \n", "    return compressed_chunk, compression_ratio"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implementing Batch Compression\n", "For efficiency, we'll compress multiple chunks in one go when possible."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def batch_compress_chunks(chunks, query, compression_type=\"selective\", model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Compress multiple chunks individually.\n", "    \n", "    Args:\n", "        chunks (List[str]): List of text chunks to compress\n", "        query (str): User query\n", "        compression_type (str): Type of compression (\"selective\", \"summary\", or \"extraction\")\n", "        model (str): LLM model to use\n", "        \n", "    Returns:\n", "        List[Tuple[str, float]]: List of compressed chunks with compression ratios\n", "    \"\"\"\n", "    print(f\"Compressing {len(chunks)} chunks...\")  # Print the number of chunks to be compressed\n", "    results = []  # Initialize an empty list to store the results\n", "    total_original_length = 0  # Initialize a variable to store the total original length of chunks\n", "    total_compressed_length = 0  # Initialize a variable to store the total compressed length of chunks\n", "    \n", "    # Iterate over each chunk\n", "    for i, chunk in enumerate(chunks):\n", "        print(f\"Compressing chunk {i+1}/{len(chunks)}...\")  # Print the progress of compression\n", "        # Compress the chunk and get the compressed chunk and compression ratio\n", "        compressed_chunk, compression_ratio = compress_chunk(chunk, query, compression_type, model)\n", "        results.append((compressed_chunk, compression_ratio))  # Append the result to the results list\n", "        \n", "        total_original_length += len(chunk)  # Add the length of the original chunk to the total original length\n", "        total_compressed_length += len(compressed_chunk)  # Add the length of the compressed chunk to the total compressed length\n", "    \n", "    # Calculate the overall compression ratio\n", "    overall_ratio = (total_original_length - total_compressed_length) / total_original_length * 100\n", "    print(f\"Overall compression ratio: {overall_ratio:.2f}%\")  # Print the overall compression ratio\n", "    \n", "    return results  # Return the list of compressed chunks with compression ratios"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Response Generation Function"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def generate_response(query, context, model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Generate a response based on the query and context.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        context (str): Context text from compressed chunks\n", "        model (str): LLM model to use\n", "        \n", "    Returns:\n", "        str: Generated response\n", "    \"\"\"\n", "    # Define the system prompt to guide the AI's behavior\n", "    system_prompt = \"\"\"You are a helpful AI assistant. Answer the user's question based only on the provided context.\n", "    If you cannot find the answer in the context, state that you don't have enough information.\"\"\"\n", "            \n", "    # Create the user prompt by combining the context and the query\n", "    user_prompt = f\"\"\"\n", "        Context:\n", "        {context}\n", "\n", "        Question: {query}\n", "\n", "        Please provide a comprehensive answer based only on the context above.\n", "    \"\"\"\n", "    \n", "    # Generate a response using the OpenAI API\n", "    response = client.chat.completions.create(\n", "        model=model,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    # Return the generated response content\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The Complete RAG Pipeline with Contextual Compression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def rag_with_compression(pdf_path, query, k=10, compression_type=\"selective\", model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Complete RAG pipeline with contextual compression.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to PDF document\n", "        query (str): User query\n", "        k (int): Number of chunks to retrieve initially\n", "        compression_type (str): Type of compression\n", "        model (str): LLM model to use\n", "        \n", "    Returns:\n", "        dict: Results including query, compressed chunks, and response\n", "    \"\"\"\n", "    print(\"\\n=== RAG WITH CONTEXTUAL COMPRESSION ===\")\n", "    print(f\"Query: {query}\")\n", "    print(f\"Compression type: {compression_type}\")\n", "    \n", "    # Process the document to extract text, chunk it, and create embeddings\n", "    vector_store = process_document(pdf_path)\n", "    \n", "    # Create an embedding for the query\n", "    query_embedding = create_embeddings(query)\n", "    \n", "    # Retrieve the top k most similar chunks based on the query embedding\n", "    print(f\"Retrieving top {k} chunks...\")\n", "    results = vector_store.similarity_search(query_embedding, k=k)\n", "    retrieved_chunks = [result[\"text\"] for result in results]\n", "    \n", "    # Apply compression to the retrieved chunks\n", "    compressed_results = batch_compress_chunks(retrieved_chunks, query, compression_type, model)\n", "    compressed_chunks = [result[0] for result in compressed_results]\n", "    compression_ratios = [result[1] for result in compressed_results]\n", "    \n", "    # Filter out any empty compressed chunks\n", "    filtered_chunks = [(chunk, ratio) for chunk, ratio in zip(compressed_chunks, compression_ratios) if chunk.strip()]\n", "    \n", "    if not filtered_chunks:\n", "        # If all chunks are compressed to empty strings, use the original chunks\n", "        print(\"Warning: All chunks were compressed to empty strings. Using original chunks.\")\n", "        filtered_chunks = [(chunk, 0.0) for chunk in retrieved_chunks]\n", "    else:\n", "        compressed_chunks, compression_ratios = zip(*filtered_chunks)\n", "    \n", "    # Generate context from the compressed chunks\n", "    context = \"\\n\\n---\\n\\n\".join(compressed_chunks)\n", "    \n", "    # Generate a response based on the compressed chunks\n", "    print(\"Generating response based on compressed chunks...\")\n", "    response = generate_response(query, context, model)\n", "    \n", "    # Prepare the result dictionary\n", "    result = {\n", "        \"query\": query,\n", "        \"original_chunks\": retrieved_chunks,\n", "        \"compressed_chunks\": compressed_chunks,\n", "        \"compression_ratios\": compression_ratios,\n", "        \"context_length_reduction\": f\"{sum(compression_ratios)/len(compression_ratios):.2f}%\",\n", "        \"response\": response\n", "    }\n", "    \n", "    print(\"\\n=== RESPONSE ===\")\n", "    print(response)\n", "    \n", "    return result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comparing RAG With and Without Compression\n", "Let's create a function to compare standard RAG with our compression-enhanced version:\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def standard_rag(pdf_path, query, k=10, model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Standard RAG without compression.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to PDF document\n", "        query (str): User query\n", "        k (int): Number of chunks to retrieve\n", "        model (str): LLM model to use\n", "        \n", "    Returns:\n", "        dict: Results including query, chunks, and response\n", "    \"\"\"\n", "    print(\"\\n=== STANDARD RAG ===\")\n", "    print(f\"Query: {query}\")\n", "    \n", "    # Process the document to extract text, chunk it, and create embeddings\n", "    vector_store = process_document(pdf_path)\n", "    \n", "    # Create an embedding for the query\n", "    query_embedding = create_embeddings(query)\n", "    \n", "    # Retrieve the top k most similar chunks based on the query embedding\n", "    print(f\"Retrieving top {k} chunks...\")\n", "    results = vector_store.similarity_search(query_embedding, k=k)\n", "    retrieved_chunks = [result[\"text\"] for result in results]\n", "    \n", "    # Generate context from the retrieved chunks\n", "    context = \"\\n\\n---\\n\\n\".join(retrieved_chunks)\n", "    \n", "    # Generate a response based on the retrieved chunks\n", "    print(\"Generating response...\")\n", "    response = generate_response(query, context, model)\n", "    \n", "    # Prepare the result dictionary\n", "    result = {\n", "        \"query\": query,\n", "        \"chunks\": retrieved_chunks,\n", "        \"response\": response\n", "    }\n", "    \n", "    print(\"\\n=== RESPONSE ===\")\n", "    print(response)\n", "    \n", "    return result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating Our Approach\n", "Now, let's implement a function to evaluate and compare the responses:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def evaluate_responses(query, responses, reference_answer):\n", "    \"\"\"\n", "    Evaluate multiple responses against a reference answer.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        responses (Dict[str, str]): Dictionary of responses by method\n", "        reference_answer (str): Reference answer\n", "        \n", "    Returns:\n", "        str: Evaluation text\n", "    \"\"\"\n", "    # Define the system prompt to guide the AI's behavior for evaluation\n", "    system_prompt = \"\"\"You are an objective evaluator of RAG responses. Compare different responses to the same query\n", "    and determine which is most accurate, comprehensive, and relevant to the query.\"\"\"\n", "    \n", "    # Create the user prompt by combining the query and reference answer\n", "    user_prompt = f\"\"\"\n", "    Query: {query}\n", "\n", "    Reference Answer: {reference_answer}\n", "\n", "    \"\"\"\n", "    \n", "    # Add each response to the prompt\n", "    for method, response in responses.items():\n", "        user_prompt += f\"\\n{method.capitalize()} Response:\\n{response}\\n\"\n", "    \n", "    # Add the evaluation criteria to the user prompt\n", "    user_prompt += \"\"\"\n", "    Please evaluate these responses based on:\n", "    1. Factual accuracy compared to the reference\n", "    2. Comprehensiveness - how completely they answer the query\n", "    3. Conciseness - whether they avoid irrelevant information\n", "    4. Overall quality\n", "\n", "    Rank the responses from best to worst with detailed explanations.\n", "    \"\"\"\n", "    \n", "    # Generate an evaluation response using the OpenAI API\n", "    evaluation_response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        temperature=0\n", "    )\n", "    \n", "    # Return the evaluation text from the response\n", "    return evaluation_response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def evaluate_compression(pdf_path, query, reference_answer=None, compression_types=[\"selective\", \"summary\", \"extraction\"]):\n", "    \"\"\"\n", "    Compare different compression techniques with standard RAG.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to PDF document\n", "        query (str): User query\n", "        reference_answer (str): Optional reference answer\n", "        compression_types (List[str]): Compression types to evaluate\n", "        \n", "    Returns:\n", "        dict: Evaluation results\n", "    \"\"\"\n", "    print(\"\\n=== EVALUATING CONTEXTUAL COMPRESSION ===\")\n", "    print(f\"Query: {query}\")\n", "    \n", "    # Run standard RAG without compression\n", "    standard_result = standard_rag(pdf_path, query)\n", "    \n", "    # Dictionary to store results of different compression techniques\n", "    compression_results = {}\n", "    \n", "    # Run RAG with each compression technique\n", "    for comp_type in compression_types:\n", "        print(f\"\\nTesting {comp_type} compression...\")\n", "        compression_results[comp_type] = rag_with_compression(pdf_path, query, compression_type=comp_type)\n", "    \n", "    # Gather responses for evaluation\n", "    responses = {\n", "        \"standard\": standard_result[\"response\"]\n", "    }\n", "    for comp_type in compression_types:\n", "        responses[comp_type] = compression_results[comp_type][\"response\"]\n", "    \n", "    # Evaluate responses if a reference answer is provided\n", "    if reference_answer:\n", "        evaluation = evaluate_responses(query, responses, reference_answer)\n", "        print(\"\\n=== EVALUATION RESULTS ===\")\n", "        print(evaluation)\n", "    else:\n", "        evaluation = \"No reference answer provided for evaluation.\"\n", "    \n", "    # Calculate metrics for each compression type\n", "    metrics = {}\n", "    for comp_type in compression_types:\n", "        metrics[comp_type] = {\n", "            \"avg_compression_ratio\": f\"{sum(compression_results[comp_type]['compression_ratios'])/len(compression_results[comp_type]['compression_ratios']):.2f}%\",\n", "            \"total_context_length\": len(\"\\n\\n\".join(compression_results[comp_type]['compressed_chunks'])),\n", "            \"original_context_length\": len(\"\\n\\n\".join(standard_result['chunks']))\n", "        }\n", "    \n", "    # Return the evaluation results, responses, and metrics\n", "    return {\n", "        \"query\": query,\n", "        \"responses\": responses,\n", "        \"evaluation\": evaluation,\n", "        \"metrics\": metrics,\n", "        \"standard_result\": standard_result,\n", "        \"compression_results\": compression_results\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running Our Complete System (Custom Query)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== EVALUATING CONTEXTUAL COMPRESSION ===\n", "Query: What are the ethical concerns surrounding the use of AI in decision-making?\n", "\n", "=== STANDARD RAG ===\n", "Query: What are the ethical concerns surrounding the use of AI in decision-making?\n", "Extracting text from PDF...\n", "Chunking text...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 chunks to the vector store\n", "Retrieving top 10 chunks...\n", "Generating response...\n", "\n", "=== RESPONSE ===\n", "The ethical concerns surrounding the use of AI in decision-making include:\n", "\n", "1. Bias and Fairness: AI systems can inherit and amplify biases present in the data they are trained on, leading to unfair or discriminatory outcomes.\n", "2. Transparency and Explainability: Many AI systems, particularly deep learning models, are \"black boxes,\" making it difficult to understand how they arrive at their decisions, which can lead to a lack of trust and accountability.\n", "3. Privacy and Data Protection: AI systems often rely on large amounts of data, raising concerns about privacy and data protection, and ensuring responsible data handling is crucial.\n", "4. Accountability and Responsibility: Establishing accountability and responsibility for AI systems is essential for addressing potential harms and ensuring ethical behavior.\n", "5. Unintended Consequences: As AI systems become more autonomous, questions arise about control, accountability, and the potential for unintended consequences.\n", "\n", "These concerns highlight the need for careful consideration of the ethical implications of AI in decision-making, including:\n", "\n", "* Ensuring fairness and mitigating bias in AI systems\n", "* Enhancing transparency and explainability in AI decision-making\n", "* Protecting sensitive information and ensuring responsible data handling\n", "* Establishing clear guidelines and ethical frameworks for AI development and deployment\n", "* Addressing the potential for unintended consequences and ensuring accountability and responsibility\n", "\n", "By addressing these concerns, we can build trust in AI systems and ensure that they are developed and deployed in a way that aligns with societal values and promotes the well-being of individuals and society.\n", "\n", "Testing selective compression...\n", "\n", "=== RAG WITH CONTEXTUAL COMPRESSION ===\n", "Query: What are the ethical concerns surrounding the use of AI in decision-making?\n", "Compression type: selective\n", "Extracting text from PDF...\n", "Chunking text...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 chunks to the vector store\n", "Retrieving top 10 chunks...\n", "Compressing 10 chunks...\n", "Compressing chunk 1/10...\n", "Compressing chunk 2/10...\n", "Compressing chunk 3/10...\n", "Compressing chunk 4/10...\n", "Compressing chunk 5/10...\n", "Compressing chunk 6/10...\n", "Compressing chunk 7/10...\n", "Compressing chunk 8/10...\n", "Compressing chunk 9/10...\n", "Compressing chunk 10/10...\n", "Overall compression ratio: 39.93%\n", "Generating response based on compressed chunks...\n", "\n", "=== RESPONSE ===\n", "The ethical concerns surrounding the use of AI in decision-making include:\n", "\n", "1. Bias and Fairness: AI systems can inherit and amplify biases present in the data they are trained on, leading to unfair or discriminatory outcomes.\n", "2. Transparency and Explainability: Many AI systems, particularly deep learning models, are \"black boxes,\" making it difficult to understand how they arrive at their decisions, which can lead to a lack of trust and accountability.\n", "3. Privacy and Data Protection: AI systems often rely on large amounts of data, raising concerns about privacy and data protection, and ensuring responsible data handling is crucial.\n", "4. Safety and Control: As AI systems become more autonomous, questions arise about control, accountability, and the potential for unintended consequences.\n", "5. Accountability and Responsibility: Establishing accountability and responsibility for AI systems is essential for addressing potential harms and ensuring ethical behavior.\n", "6. Economic and Social Impacts: Addressing the potential economic and social impacts of AI-driven automation is a key challenge.\n", "7. Respect for Human Rights: AI systems should be designed to respect human rights, including privacy, non-discrimination, and beneficence.\n", "8. Robustness and Reliability: Ensuring that AI systems are robust and reliable is essential for building trust.\n", "9. Empowerment of Users: Empowering users with control over AI systems and providing them with agency in their interactions with AI enhances trust.\n", "10. Ethical Considerations in Design and Development: Incorporating ethical considerations into the design and development of AI systems is crucial for building trust.\n", "\n", "These concerns highlight the need for a comprehensive approach to addressing the ethical implications of AI in decision-making, including the development of clear guidelines, ethical frameworks, and regulations to ensure that AI systems are designed and deployed in a responsible and trustworthy manner.\n", "\n", "Testing summary compression...\n", "\n", "=== RAG WITH CONTEXTUAL COMPRESSION ===\n", "Query: What are the ethical concerns surrounding the use of AI in decision-making?\n", "Compression type: summary\n", "Extracting text from PDF...\n", "Chunking text...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 chunks to the vector store\n", "Retrieving top 10 chunks...\n", "Compressing 10 chunks...\n", "Compressing chunk 1/10...\n", "Compressing chunk 2/10...\n", "Compressing chunk 3/10...\n", "Compressing chunk 4/10...\n", "Compressing chunk 5/10...\n", "Compressing chunk 6/10...\n", "Compressing chunk 7/10...\n", "Compressing chunk 8/10...\n", "Compressing chunk 9/10...\n", "Compressing chunk 10/10...\n", "Overall compression ratio: 63.87%\n", "Generating response based on compressed chunks...\n", "\n", "=== RESPONSE ===\n", "The ethical concerns surrounding the use of AI in decision-making include:\n", "\n", "1. Bias and Fairness: AI systems can inherit and amplify biases present in the data they are trained on, leading to unfair or discriminatory outcomes.\n", "2. Transparency and Explainability: Many AI systems, particularly deep learning models, are \"black boxes,\" making it difficult to understand how they arrive at their decisions.\n", "3. Privacy and Security: The reliance on large amounts of data raises concerns about data security and the potential for unauthorized access or misuse.\n", "4. Job Displacement: The potential for AI systems to automate repetitive or routine tasks raises concerns about job displacement and the impact on workers.\n", "5. Control, Accountability, and Unintended Consequences: As AI systems become more autonomous, there are concerns about who is responsible for their actions, and the potential for unintended consequences.\n", "6. Need for Clear Guidelines and Ethical Frameworks: There is a need for clear guidelines and ethical frameworks to ensure that AI systems are developed and deployed in a responsible and ethical manner.\n", "\n", "These concerns highlight the importance of addressing the ethical implications of AI in decision-making, and the need for a balanced approach that promotes innovation while protecting human rights, privacy, and well-being.\n", "\n", "Testing extraction compression...\n", "\n", "=== RAG WITH CONTEXTUAL COMPRESSION ===\n", "Query: What are the ethical concerns surrounding the use of AI in decision-making?\n", "Compression type: extraction\n", "Extracting text from PDF...\n", "Chunking text...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 chunks to the vector store\n", "Retrieving top 10 chunks...\n", "Compressing 10 chunks...\n", "Compressing chunk 1/10...\n", "Compressing chunk 2/10...\n", "Compressing chunk 3/10...\n", "Compressing chunk 4/10...\n", "Compressing chunk 5/10...\n", "Compressing chunk 6/10...\n", "Compressing chunk 7/10...\n", "Compressing chunk 8/10...\n", "Compressing chunk 9/10...\n", "Compressing chunk 10/10...\n", "Overall compression ratio: 54.41%\n", "Generating response based on compressed chunks...\n", "\n", "=== RESPONSE ===\n", "The ethical concerns surrounding the use of AI in decision-making include:\n", "\n", "1. Bias and Fairness: AI systems can inherit and amplify biases present in the data they are trained on, leading to unfair or discriminatory outcomes. Ensuring fairness and mitigating bias in AI systems is a critical challenge.\n", "\n", "2. <PERSON>k of Transparency and Explainability: Many AI systems, particularly deep learning models, are \"black boxes,\" making it difficult to understand how they arrive at their decisions. This lack of transparency and explainability can lead to a lack of trust in AI systems.\n", "\n", "3. Accountability and Responsibility: Establishing accountability and responsibility for AI systems is essential for addressing potential harms and ensuring ethical behavior. This includes defining roles and responsibilities for developers, deployers, and users of AI systems.\n", "\n", "4. Respect for Human Rights, Privacy, and Non-Discrimination: AI systems must be designed and deployed in a way that respects human rights, protects privacy, and avoids non-discrimination.\n", "\n", "5. Beneficence: AI systems should be designed and deployed in a way that promotes the well-being and benefit of society.\n", "\n", "6. Addressing Bias in Data Collection, Algorithm Design, and Ongoing Monitoring and Evaluation: Addressing bias requires careful data collection, algorithm design, and ongoing monitoring and evaluation.\n", "\n", "7. Ensuring Robustness and Reliability: Ensuring that AI systems are robust and reliable is essential for building trust. This includes testing and validating AI models, monitoring their performance, and addressing potential vulnerabilities.\n", "\n", "8. Empowering Users with Control: Empowering users with control over AI systems and providing them with agency in their interactions with AI enhances trust. This includes allowing users to customize AI settings, understand how their data is used, and opt out of AI-driven features.\n", "\n", "9. International Discussions and Regulations: The potential use of AI in autonomous weapons systems raises significant ethical and security concerns, and international discussions and regulations are needed to address the risks associated with AI-powered weapons.\n", "\n", "10. Public Perception and Trust: Public perception and trust in AI are essential for its widespread adoption and positive social impact.\n", "\n", "=== EVALUATION RESULTS ===\n", "Based on the evaluation criteria, here are the rankings from best to worst:\n", "\n", "1. **Reference Answer**: This response is the most accurate, comprehensive, and relevant to the query. It provides a clear and concise overview of the ethical concerns surrounding the use of AI in decision-making, covering topics such as bias, transparency, privacy, accountability, and job displacement. The response is well-structured and easy to follow, making it an excellent example of a high-quality response.\n", "\n", "2. **Standard Response**: This response is comprehensive and covers all the key ethical concerns surrounding AI in decision-making. It provides a clear and concise overview of the issues, including bias, transparency, privacy, accountability, and job displacement. The response is well-organized and easy to follow, making it a strong contender for the top spot.\n", "\n", "3. **Selective Response**: This response is comprehensive, but it lacks some of the key points mentioned in the reference answer. It covers bias, transparency, privacy, and accountability, but misses out on job displacement and control. The response is still well-organized and easy to follow, but it falls short of the reference answer in terms of comprehensiveness.\n", "\n", "4. **Summary Response**: This response is concise and covers the key points, but it lacks some of the detail and depth of the reference answer. It provides a good overview of the ethical concerns, but it doesn't delve as deeply into the issues as the reference answer. The response is still clear and easy to follow, but it falls short of the standard response in terms of comprehensiveness.\n", "\n", "5. **Extraction Response**: This response is concise and covers the key points, but it lacks some of the detail and depth of the reference answer. It provides a good overview of the ethical concerns, but it doesn't delve as deeply into the issues as the reference answer. The response is still clear and easy to follow, but it falls short of the standard response in terms of comprehensiveness.\n", "\n", "Ranking Criteria:\n", "\n", "* Factual accuracy: Reference Answer (9/10), Standard Response (8.5/10), Selective Response (8/10), Summary Response (7.5/10), Extraction Response (7/10)\n", "* Comprehensiveness: Reference Answer (9/10), Standard Response (8.5/10), Selective Response (8/10), Summary Response (7.5/10), Extraction Response (7/10)\n", "* Conciseness: Summary Response (8/10), Extraction Response (7.5/10), Selective Response (7/10), Standard Response (6.5/10), Reference Answer (6/10)\n", "* Overall quality: Reference Answer (9/10), Standard Response (8.5/10), Selective Response (8/10), Summary Response (7.5/10), Extraction Response (7/10)\n", "\n", "Note: The scores are subjective and based on the evaluation criteria. They are intended to provide a general ranking of the responses rather than a precise numerical score.\n"]}], "source": ["# Path to the PDF document containing information on AI ethics  \n", "pdf_path = \"data/AI_Information.pdf\" \n", "\n", "# Query to extract relevant information from the document  \n", "query = \"What are the ethical concerns surrounding the use of AI in decision-making?\"  \n", "\n", "# Optional reference answer for evaluation  \n", "reference_answer = \"\"\"  \n", "The use of AI in decision-making raises several ethical concerns.  \n", "- Bias in AI models can lead to unfair or discriminatory outcomes, especially in critical areas like hiring, lending, and law enforcement.  \n", "- Lack of transparency and explainability in AI-driven decisions makes it difficult for individuals to challenge unfair outcomes.  \n", "- Privacy risks arise as AI systems process vast amounts of personal data, often without explicit consent.  \n", "- The potential for job displacement due to automation raises social and economic concerns.  \n", "- AI decision-making may also concentrate power in the hands of a few large tech companies, leading to accountability challenges.  \n", "- Ensuring fairness, accountability, and transparency in AI systems is essential for ethical deployment.  \n", "\"\"\"  \n", "\n", "# Run evaluation with different compression techniques  \n", "# Compression types:  \n", "# - \"selective\": Retains key details while omitting less relevant parts  \n", "# - \"summary\": Provides a concise version of the information  \n", "# - \"extraction\": Extracts relevant sentences verbatim from the document  \n", "results = evaluate_compression(  \n", "    pdf_path=pdf_path,  \n", "    query=query,  \n", "    reference_answer=reference_answer,  \n", "    compression_types=[\"selective\", \"summary\", \"extraction\"]  \n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing Compression Results"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def visualize_compression_results(evaluation_results):\n", "    \"\"\"\n", "    Visualize the results of different compression techniques.\n", "    \n", "    Args:\n", "        evaluation_results (Dict): Results from evaluate_compression function\n", "    \"\"\"\n", "    # Extract the query and standard chunks from the evaluation results\n", "    query = evaluation_results[\"query\"]\n", "    standard_chunks = evaluation_results[\"standard_result\"][\"chunks\"]\n", "    \n", "    # Print the query\n", "    print(f\"Query: {query}\")\n", "    print(\"\\n\" + \"=\"*80 + \"\\n\")\n", "    \n", "    # Get a sample chunk to visualize (using the first chunk)\n", "    original_chunk = standard_chunks[0]\n", "    \n", "    # Iterate over each compression type and show a comparison\n", "    for comp_type in evaluation_results[\"compression_results\"].keys():\n", "        compressed_chunks = evaluation_results[\"compression_results\"][comp_type][\"compressed_chunks\"]\n", "        compression_ratios = evaluation_results[\"compression_results\"][comp_type][\"compression_ratios\"]\n", "        \n", "        # Get the corresponding compressed chunk and its compression ratio\n", "        compressed_chunk = compressed_chunks[0]\n", "        compression_ratio = compression_ratios[0]\n", "        \n", "        print(f\"\\n=== {comp_type.upper()} COMPRESSION EXAMPLE ===\\n\")\n", "        \n", "        # Show the original chunk (truncated if too long)\n", "        print(\"ORIGINAL CHUNK:\")\n", "        print(\"-\" * 40)\n", "        if len(original_chunk) > 800:\n", "            print(original_chunk[:800] + \"... [truncated]\")\n", "        else:\n", "            print(original_chunk)\n", "        print(\"-\" * 40)\n", "        print(f\"Length: {len(original_chunk)} characters\\n\")\n", "        \n", "        # Show the compressed chunk\n", "        print(\"COMPRESSED CHUNK:\")\n", "        print(\"-\" * 40)\n", "        print(compressed_chunk)\n", "        print(\"-\" * 40)\n", "        print(f\"Length: {len(compressed_chunk)} characters\")\n", "        print(f\"Compression ratio: {compression_ratio:.2f}%\\n\")\n", "        \n", "        # Show overall statistics for this compression type\n", "        avg_ratio = sum(compression_ratios) / len(compression_ratios)\n", "        print(f\"Average compression across all chunks: {avg_ratio:.2f}%\")\n", "        print(f\"Total context length reduction: {evaluation_results['metrics'][comp_type]['avg_compression_ratio']}\")\n", "        print(\"=\" * 80)\n", "    \n", "    # Show a summary table of compression techniques\n", "    print(\"\\n=== COMPRESSION SUMMARY ===\\n\")\n", "    print(f\"{'Technique':<15} {'Avg Ratio':<15} {'Context Length':<15} {'Original Length':<15}\")\n", "    print(\"-\" * 60)\n", "    \n", "    # Print the metrics for each compression type\n", "    for comp_type, metrics in evaluation_results[\"metrics\"].items():\n", "        print(f\"{comp_type:<15} {metrics['avg_compression_ratio']:<15} {metrics['total_context_length']:<15} {metrics['original_context_length']:<15}\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: What are the ethical concerns surrounding the use of AI in decision-making?\n", "\n", "================================================================================\n", "\n", "\n", "=== SELECTIVE COMPRESSION EXAMPLE ===\n", "\n", "ORIGINAL CHUNK:\n", "----------------------------------------\n", "inability \n", "Many AI systems, particularly deep learning models, are \"black boxes,\" making it difficult to \n", "understand how they arrive at their decisions. Enhancing transparency and explainability is \n", "crucial for building trust and accountability. \n", " \n", " \n", "Privacy and Security \n", "AI systems often rely on large amounts of data, raising concerns about privacy and data security. \n", "Protecting sensitive information and ensuring responsible data handling are essential. \n", "Job Displacement \n", "The automation capabilities of AI have raised concerns about job displacement, particularly in \n", "industries with repetitive or routine tasks. Addressing the potential economic and social impacts \n", "of AI-driven automation is a key challenge. \n", "Autonomy and Control \n", "As AI systems become more autonomous, questions arise about ... [truncated]\n", "----------------------------------------\n", "Length: 1000 characters\n", "\n", "COMPRESSED CHUNK:\n", "----------------------------------------\n", "Many AI systems, particularly deep learning models, are \"black boxes,\" making it difficult to \n", "understand how they arrive at their decisions. Enhancing transparency and explainability is \n", "crucial for building trust and accountability.\n", "\n", "Establishing clear guidelines and ethical frameworks for AI development and deployment is crucial.\n", "\n", "Protecting sensitive information and ensuring responsible data handling are essential.\n", "\n", "Addressing the potential economic and social impacts of AI-driven automation is a key challenge.\n", "\n", "As AI systems become more autonomous, questions arise about control, accountability, and the \n", "potential for unintended consequences.\n", "----------------------------------------\n", "Length: 654 characters\n", "Compression ratio: 34.60%\n", "\n", "Average compression across all chunks: 39.93%\n", "Total context length reduction: 39.93%\n", "================================================================================\n", "\n", "=== SUMMARY COMPRESSION EXAMPLE ===\n", "\n", "ORIGINAL CHUNK:\n", "----------------------------------------\n", "inability \n", "Many AI systems, particularly deep learning models, are \"black boxes,\" making it difficult to \n", "understand how they arrive at their decisions. Enhancing transparency and explainability is \n", "crucial for building trust and accountability. \n", " \n", " \n", "Privacy and Security \n", "AI systems often rely on large amounts of data, raising concerns about privacy and data security. \n", "Protecting sensitive information and ensuring responsible data handling are essential. \n", "Job Displacement \n", "The automation capabilities of AI have raised concerns about job displacement, particularly in \n", "industries with repetitive or routine tasks. Addressing the potential economic and social impacts \n", "of AI-driven automation is a key challenge. \n", "Autonomy and Control \n", "As AI systems become more autonomous, questions arise about ... [truncated]\n", "----------------------------------------\n", "Length: 1000 characters\n", "\n", "COMPRESSED CHUNK:\n", "----------------------------------------\n", "The ethical concerns surrounding the use of AI in decision-making include:\n", "\n", "- Lack of transparency and explainability in AI decision-making processes\n", "- Privacy and data security concerns due to reliance on large amounts of data\n", "- Potential for job displacement, particularly in industries with repetitive or routine tasks\n", "- Questions about control, accountability, and unintended consequences as AI systems become more autonomous\n", "- Need for clear guidelines and ethical frameworks for AI development and deployment\n", "----------------------------------------\n", "Length: 514 characters\n", "Compression ratio: 48.60%\n", "\n", "Average compression across all chunks: 63.87%\n", "Total context length reduction: 63.87%\n", "================================================================================\n", "\n", "=== EXTRACTION COMPRESSION EXAMPLE ===\n", "\n", "ORIGINAL CHUNK:\n", "----------------------------------------\n", "inability \n", "Many AI systems, particularly deep learning models, are \"black boxes,\" making it difficult to \n", "understand how they arrive at their decisions. Enhancing transparency and explainability is \n", "crucial for building trust and accountability. \n", " \n", " \n", "Privacy and Security \n", "AI systems often rely on large amounts of data, raising concerns about privacy and data security. \n", "Protecting sensitive information and ensuring responsible data handling are essential. \n", "Job Displacement \n", "The automation capabilities of AI have raised concerns about job displacement, particularly in \n", "industries with repetitive or routine tasks. Addressing the potential economic and social impacts \n", "of AI-driven automation is a key challenge. \n", "Autonomy and Control \n", "As AI systems become more autonomous, questions arise about ... [truncated]\n", "----------------------------------------\n", "Length: 1000 characters\n", "\n", "COMPRESSED CHUNK:\n", "----------------------------------------\n", "Many AI systems, particularly deep learning models, are \"black boxes,\" making it difficult to \n", "understand how they arrive at their decisions. Enhancing transparency and explainability is \n", "crucial for building trust and accountability. \n", "\n", "Establishing clear guidelines and ethical frameworks for AI development and deployment is crucial.\n", "----------------------------------------\n", "Length: 335 characters\n", "Compression ratio: 66.50%\n", "\n", "Average compression across all chunks: 54.41%\n", "Total context length reduction: 54.41%\n", "================================================================================\n", "\n", "=== COMPRESSION SUMMARY ===\n", "\n", "Technique       Avg Ratio       Context Length  Original Length\n", "------------------------------------------------------------\n", "selective       39.93%          6025            10018          \n", "summary         63.87%          3631            10018          \n", "extraction      54.41%          4577            10018          \n"]}], "source": ["# Visualize the compression results\n", "visualize_compression_results(results)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}