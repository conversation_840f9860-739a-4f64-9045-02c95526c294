{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["# Query Transformations for Enhanced RAG Systems\n", "\n", "This notebook implements three query transformation techniques to enhance retrieval performance in RAG systems without relying on specialized libraries like LangChain. By modifying user queries, we can significantly improve the relevance and comprehensiveness of retrieved information.\n", "\n", "## Key Transformation Techniques\n", "\n", "1. **Query Rewriting**: Makes queries more specific and detailed for better search precision.\n", "2. **Step-back Prompting**: Generates broader queries to retrieve useful contextual information.\n", "3. **Sub-query Decomposition**: Breaks complex queries into simpler components for comprehensive retrieval."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "We begin by importing necessary libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import fitz\n", "import os\n", "import numpy as np\n", "import json\n", "from openai import OpenAI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the OpenAI API Client\n", "We initialize the OpenAI client to generate embeddings and responses."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Initialize the OpenAI client with the base URL and API key\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",\n", "    api_key=os.getenv(\"OPENAI_API_KEY\")  # Retrieve the API key from environment variables\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implementing Query Transformation Techniques\n", "### 1. Query Rewriting\n", "This technique makes queries more specific and detailed to improve precision in retrieval."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def rewrite_query(original_query, model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Rewrites a query to make it more specific and detailed for better retrieval.\n", "    \n", "    Args:\n", "        original_query (str): The original user query\n", "        model (str): The model to use for query rewriting\n", "        \n", "    Returns:\n", "        str: The rewritten query\n", "    \"\"\"\n", "    # Define the system prompt to guide the AI assistant's behavior\n", "    system_prompt = \"You are an AI assistant specialized in improving search queries. Your task is to rewrite user queries to be more specific, detailed, and likely to retrieve relevant information.\"\n", "    \n", "    # Define the user prompt with the original query to be rewritten\n", "    user_prompt = f\"\"\"\n", "    Rewrite the following query to make it more specific and detailed. Include relevant terms and concepts that might help in retrieving accurate information.\n", "    \n", "    Original query: {original_query}\n", "    \n", "    Rewritten query:\n", "    \"\"\"\n", "    \n", "    # Generate the rewritten query using the specified model\n", "    response = client.chat.completions.create(\n", "        model=model,\n", "        temperature=0.0,  # Low temperature for deterministic output\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ]\n", "    )\n", "    \n", "    # Return the rewritten query, stripping any leading/trailing whitespace\n", "    return response.choices[0].message.content.strip()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Step-back Prompting\n", "This technique generates broader queries to retrieve contextual background information."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def generate_step_back_query(original_query, model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Generates a more general 'step-back' query to retrieve broader context.\n", "    \n", "    Args:\n", "        original_query (str): The original user query\n", "        model (str): The model to use for step-back query generation\n", "        \n", "    Returns:\n", "        str: The step-back query\n", "    \"\"\"\n", "    # Define the system prompt to guide the AI assistant's behavior\n", "    system_prompt = \"You are an AI assistant specialized in search strategies. Your task is to generate broader, more general versions of specific queries to retrieve relevant background information.\"\n", "    \n", "    # Define the user prompt with the original query to be generalized\n", "    user_prompt = f\"\"\"\n", "    Generate a broader, more general version of the following query that could help retrieve useful background information.\n", "    \n", "    Original query: {original_query}\n", "    \n", "    Step-back query:\n", "    \"\"\"\n", "    \n", "    # Generate the step-back query using the specified model\n", "    response = client.chat.completions.create(\n", "        model=model,\n", "        temperature=0.1,  # Slightly higher temperature for some variation\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ]\n", "    )\n", "    \n", "    # Return the step-back query, stripping any leading/trailing whitespace\n", "    return response.choices[0].message.content.strip()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. Sub-query Decomposition\n", "This technique breaks down complex queries into simpler components for comprehensive retrieval."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def decompose_query(original_query, num_subqueries=4, model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Decomposes a complex query into simpler sub-queries.\n", "    \n", "    Args:\n", "        original_query (str): The original complex query\n", "        num_subqueries (int): Number of sub-queries to generate\n", "        model (str): The model to use for query decomposition\n", "        \n", "    Returns:\n", "        List[str]: A list of simpler sub-queries\n", "    \"\"\"\n", "    # Define the system prompt to guide the AI assistant's behavior\n", "    system_prompt = \"You are an AI assistant specialized in breaking down complex questions. Your task is to decompose complex queries into simpler sub-questions that, when answered together, address the original query.\"\n", "    \n", "    # Define the user prompt with the original query to be decomposed\n", "    user_prompt = f\"\"\"\n", "    Break down the following complex query into {num_subqueries} simpler sub-queries. Each sub-query should focus on a different aspect of the original question.\n", "    \n", "    Original query: {original_query}\n", "    \n", "    Generate {num_subqueries} sub-queries, one per line, in this format:\n", "    1. [First sub-query]\n", "    2. [Second sub-query]\n", "    And so on...\n", "    \"\"\"\n", "    \n", "    # Generate the sub-queries using the specified model\n", "    response = client.chat.completions.create(\n", "        model=model,\n", "        temperature=0.2,  # Slightly higher temperature for some variation\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ]\n", "    )\n", "    \n", "    # Process the response to extract sub-queries\n", "    content = response.choices[0].message.content.strip()\n", "    \n", "    # Extract numbered queries using simple parsing\n", "    lines = content.split(\"\\n\")\n", "    sub_queries = []\n", "    \n", "    for line in lines:\n", "        if line.strip() and any(line.strip().startswith(f\"{i}.\") for i in range(1, 10)):\n", "            # Remove the number and leading space\n", "            query = line.strip()\n", "            query = query[query.find(\".\")+1:].strip()\n", "            sub_queries.append(query)\n", "    \n", "    return sub_queries"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Demonstrating Query Transformation Techniques\n", "Let's apply these techniques to an example query."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original Query: What are the impacts of AI on job automation and employment?\n", "\n", "1. Rewritten Query:\n", "Rewritten query: What are the specific effects of Artificial Intelligence (AI) and Machine Learning (ML) on job displacement, workforce automation, and employment rates across various industries, including manufacturing, customer service, and data analysis, and what are the potential consequences for workers, businesses, and the overall economy in terms of job creation, skills training, and social implications?\n", "\n", "This rewritten query includes more specific terms and concepts, such as:\n", "\n", "* Artificial Intelligence (AI) and Machine Learning (ML) to clarify the technologies involved\n", "* Job displacement and workforce automation to specify the aspects of employment being impacted\n", "* Various industries (manufacturing, customer service, data analysis) to provide context and scope\n", "* Employment rates to quantify the potential effects\n", "* Workers, businesses, and the overall economy to consider the broader social and economic implications\n", "* Job creation, skills training, and social implications to explore the potential consequences and mitigation strategies\n", "\n", "By including these specific terms and concepts, the rewritten query is more likely to retrieve accurate and relevant information on the topic.\n", "\n", "2. Step-back Query:\n", "Here's a broader, more general version of the query:\n", "\n", "    Step-back query: What are the effects of technological advancements on the labor market and workforce?\n", "\n", "This revised query takes a step back from the specific focus on AI and job automation, and instead explores the broader topic of how technological advancements in general are influencing the labor market and workforce. This can help retrieve useful background information on the historical context, trends, and research related to the impact of technology on employment, which can provide a foundation for understanding the more specific issue of AI's impact on job automation.\n", "\n", "3. Sub-queries:\n", "   1. What types of jobs are most likely to be automated by AI, and which industries will be most affected?\n", "   2. How will AI-driven automation change the nature of work and the skills required for various professions?\n", "   3. What are the potential economic impacts of AI on employment, including effects on job creation, job displacement, and income inequality?\n", "   4. What strategies can governments, educational institutions, and businesses implement to mitigate the negative effects of AI on employment and ensure workers are prepared for an AI-driven job market?\n"]}], "source": ["# Example query\n", "original_query = \"What are the impacts of AI on job automation and employment?\"\n", "\n", "# Apply query transformations\n", "print(\"Original Query:\", original_query)\n", "\n", "# Query Rewriting\n", "rewritten_query = rewrite_query(original_query)\n", "print(\"\\n1. Rewritten Query:\")\n", "print(rewritten_query)\n", "\n", "# Step-back Prompting\n", "step_back_query = generate_step_back_query(original_query)\n", "print(\"\\n2. Step-back Query:\")\n", "print(step_back_query)\n", "\n", "# Sub-query Decomposition\n", "sub_queries = decompose_query(original_query, num_subqueries=4)\n", "print(\"\\n3. Sub-queries:\")\n", "for i, query in enumerate(sub_queries, 1):\n", "    print(f\"   {i}. {query}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building a Simple Vector Store\n", "To demonstrate how query transformations integrate with retrieval, let's implement a simple vector store."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["class SimpleVectorStore:\n", "    \"\"\"\n", "    A simple vector store implementation using NumPy.\n", "    \"\"\"\n", "    def __init__(self):\n", "        \"\"\"\n", "        Initialize the vector store.\n", "        \"\"\"\n", "        self.vectors = []  # List to store embedding vectors\n", "        self.texts = []  # List to store original texts\n", "        self.metadata = []  # List to store metadata for each text\n", "    \n", "    def add_item(self, text, embedding, metadata=None):\n", "        \"\"\"\n", "        Add an item to the vector store.\n", "\n", "        Args:\n", "        text (str): The original text.\n", "        embedding (List[float]): The embedding vector.\n", "        metadata (dict, optional): Additional metadata.\n", "        \"\"\"\n", "        self.vectors.append(np.array(embedding))  # Convert embedding to numpy array and add to vectors list\n", "        self.texts.append(text)  # Add the original text to texts list\n", "        self.metadata.append(metadata or {})  # Add metadata to metadata list, use empty dict if None\n", "    \n", "    def similarity_search(self, query_embedding, k=5):\n", "        \"\"\"\n", "        Find the most similar items to a query embedding.\n", "\n", "        Args:\n", "        query_embedding (List[float]): Query embedding vector.\n", "        k (int): Number of results to return.\n", "\n", "        Returns:\n", "        List[Dict]: Top k most similar items with their texts and metadata.\n", "        \"\"\"\n", "        if not self.vectors:\n", "            return []  # Return empty list if no vectors are stored\n", "        \n", "        # Convert query embedding to numpy array\n", "        query_vector = np.array(query_embedding)\n", "        \n", "        # Calculate similarities using cosine similarity\n", "        similarities = []\n", "        for i, vector in enumerate(self.vectors):\n", "            # Compute cosine similarity between query vector and stored vector\n", "            similarity = np.dot(query_vector, vector) / (np.linalg.norm(query_vector) * np.linalg.norm(vector))\n", "            similarities.append((i, similarity))  # Append index and similarity score\n", "        \n", "        # Sort by similarity (descending)\n", "        similarities.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        # Return top k results\n", "        results = []\n", "        for i in range(min(k, len(similarities))):\n", "            idx, score = similarities[i]\n", "            results.append({\n", "                \"text\": self.texts[idx],  # Add the corresponding text\n", "                \"metadata\": self.metadata[idx],  # Add the corresponding metadata\n", "                \"similarity\": score  # Add the similarity score\n", "            })\n", "        \n", "        return results  # Return the list of top k similar items"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Embeddings"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def create_embeddings(text, model=\"BAAI/bge-en-icl\"):\n", "    \"\"\"\n", "    Creates embeddings for the given text using the specified OpenAI model.\n", "\n", "    Args:\n", "    text (str): The input text for which embeddings are to be created.\n", "    model (str): The model to be used for creating embeddings.\n", "\n", "    Returns:\n", "    List[float]: The embedding vector.\n", "    \"\"\"\n", "    # Handle both string and list inputs by converting string input to a list\n", "    input_text = text if isinstance(text, list) else [text]\n", "    \n", "    # Create embeddings for the input text using the specified model\n", "    response = client.embeddings.create(\n", "        model=model,\n", "        input=input_text\n", "    )\n", "    \n", "    # If input was a string, return just the first embedding\n", "    if isinstance(text, str):\n", "        return response.data[0].embedding\n", "    \n", "    # Otherwise, return all embeddings as a list of vectors\n", "    return [item.embedding for item in response.data]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implementing RAG with Query Transformations"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def extract_text_from_pdf(pdf_path):\n", "    \"\"\"\n", "    Extracts text from a PDF file.\n", "\n", "    Args:\n", "    pdf_path (str): Path to the PDF file.\n", "\n", "    Returns:\n", "    str: Extracted text from the PDF.\n", "    \"\"\"\n", "    # Open the PDF file\n", "    mypdf = fitz.open(pdf_path)\n", "    all_text = \"\"  # Initialize an empty string to store the extracted text\n", "\n", "    # Iterate through each page in the PDF\n", "    for page_num in range(mypdf.page_count):\n", "        page = mypdf[page_num]  # Get the page\n", "        text = page.get_text(\"text\")  # Extract text from the page\n", "        all_text += text  # Append the extracted text to the all_text string\n", "\n", "    return all_text  # Return the extracted text"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def chunk_text(text, n=1000, overlap=200):\n", "    \"\"\"\n", "    Chunks the given text into segments of n characters with overlap.\n", "\n", "    Args:\n", "    text (str): The text to be chunked.\n", "    n (int): The number of characters in each chunk.\n", "    overlap (int): The number of overlapping characters between chunks.\n", "\n", "    Returns:\n", "    List[str]: A list of text chunks.\n", "    \"\"\"\n", "    chunks = []  # Initialize an empty list to store the chunks\n", "    \n", "    # Loop through the text with a step size of (n - overlap)\n", "    for i in range(0, len(text), n - overlap):\n", "        # Append a chunk of text from index i to i + n to the chunks list\n", "        chunks.append(text[i:i + n])\n", "\n", "    return chunks  # Return the list of text chunks"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def process_document(pdf_path, chunk_size=1000, chunk_overlap=200):\n", "    \"\"\"\n", "    Process a document for RAG.\n", "\n", "    Args:\n", "    pdf_path (str): Path to the PDF file.\n", "    chunk_size (int): Size of each chunk in characters.\n", "    chunk_overlap (int): Overlap between chunks in characters.\n", "\n", "    Returns:\n", "    SimpleVectorStore: A vector store containing document chunks and their embeddings.\n", "    \"\"\"\n", "    print(\"Extracting text from PDF...\")\n", "    extracted_text = extract_text_from_pdf(pdf_path)\n", "    \n", "    print(\"Chunking text...\")\n", "    chunks = chunk_text(extracted_text, chunk_size, chunk_overlap)\n", "    print(f\"Created {len(chunks)} text chunks\")\n", "    \n", "    print(\"Creating embeddings for chunks...\")\n", "    # Create embeddings for all chunks at once for efficiency\n", "    chunk_embeddings = create_embeddings(chunks)\n", "    \n", "    # Create vector store\n", "    store = SimpleVectorStore()\n", "    \n", "    # Add chunks to vector store\n", "    for i, (chunk, embedding) in enumerate(zip(chunks, chunk_embeddings)):\n", "        store.add_item(\n", "            text=chunk,\n", "            embedding=embedding,\n", "            metadata={\"index\": i, \"source\": pdf_path}\n", "        )\n", "    \n", "    print(f\"Added {len(chunks)} chunks to the vector store\")\n", "    return store"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RAG with Query Transformations"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def transformed_search(query, vector_store, transformation_type, top_k=3):\n", "    \"\"\"\n", "    Search using a transformed query.\n", "    \n", "    Args:\n", "        query (str): Original query\n", "        vector_store (SimpleVectorStore): Vector store to search\n", "        transformation_type (str): Type of transformation ('rewrite', 'step_back', or 'decompose')\n", "        top_k (int): Number of results to return\n", "        \n", "    Returns:\n", "        List[Dict]: Search results\n", "    \"\"\"\n", "    print(f\"Transformation type: {transformation_type}\")\n", "    print(f\"Original query: {query}\")\n", "    \n", "    results = []\n", "    \n", "    if transformation_type == \"rewrite\":\n", "        # Query rewriting\n", "        transformed_query = rewrite_query(query)\n", "        print(f\"Rewritten query: {transformed_query}\")\n", "        \n", "        # Create embedding for transformed query\n", "        query_embedding = create_embeddings(transformed_query)\n", "        \n", "        # Search with rewritten query\n", "        results = vector_store.similarity_search(query_embedding, k=top_k)\n", "        \n", "    elif transformation_type == \"step_back\":\n", "        # Step-back prompting\n", "        transformed_query = generate_step_back_query(query)\n", "        print(f\"Step-back query: {transformed_query}\")\n", "        \n", "        # Create embedding for transformed query\n", "        query_embedding = create_embeddings(transformed_query)\n", "        \n", "        # Search with step-back query\n", "        results = vector_store.similarity_search(query_embedding, k=top_k)\n", "        \n", "    elif transformation_type == \"decompose\":\n", "        # Sub-query decomposition\n", "        sub_queries = decompose_query(query)\n", "        print(\"Decomposed into sub-queries:\")\n", "        for i, sub_q in enumerate(sub_queries, 1):\n", "            print(f\"{i}. {sub_q}\")\n", "        \n", "        # Create embeddings for all sub-queries\n", "        sub_query_embeddings = create_embeddings(sub_queries)\n", "        \n", "        # Search with each sub-query and combine results\n", "        all_results = []\n", "        for i, embedding in enumerate(sub_query_embeddings):\n", "            sub_results = vector_store.similarity_search(embedding, k=2)  # Get fewer results per sub-query\n", "            all_results.extend(sub_results)\n", "        \n", "        # Remove duplicates (keep highest similarity score)\n", "        seen_texts = {}\n", "        for result in all_results:\n", "            text = result[\"text\"]\n", "            if text not in seen_texts or result[\"similarity\"] > seen_texts[text][\"similarity\"]:\n", "                seen_texts[text] = result\n", "        \n", "        # Sort by similarity and take top_k\n", "        results = sorted(seen_texts.values(), key=lambda x: x[\"similarity\"], reverse=True)[:top_k]\n", "        \n", "    else:\n", "        # Regular search without transformation\n", "        query_embedding = create_embeddings(query)\n", "        results = vector_store.similarity_search(query_embedding, k=top_k)\n", "    \n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generating a Response with Transformed Queries"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def generate_response(query, context, model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Generates a response based on the query and retrieved context.\n", "    \n", "    Args:\n", "        query (str): User query\n", "        context (str): Retrieved context\n", "        model (str): The model to use for response generation\n", "        \n", "    Returns:\n", "        str: Generated response\n", "    \"\"\"\n", "    # Define the system prompt to guide the AI assistant's behavior\n", "    system_prompt = \"You are a helpful AI assistant. Answer the user's question based only on the provided context. If you cannot find the answer in the context, state that you don't have enough information.\"\n", "    \n", "    # Define the user prompt with the context and query\n", "    user_prompt = f\"\"\"\n", "        Context:\n", "        {context}\n", "\n", "        Question: {query}\n", "\n", "        Please provide a comprehensive answer based only on the context above.\n", "    \"\"\"\n", "    \n", "    # Generate the response using the specified model\n", "    response = client.chat.completions.create(\n", "        model=model,\n", "        temperature=0,  # Low temperature for deterministic output\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ]\n", "    )\n", "    \n", "    # Return the generated response, stripping any leading/trailing whitespace\n", "    return response.choices[0].message.content.strip()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running the Complete RAG Pipeline with Query Transformations"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def rag_with_query_transformation(pdf_path, query, transformation_type=None):\n", "    \"\"\"\n", "    Run complete RAG pipeline with optional query transformation.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to PDF document\n", "        query (str): User query\n", "        transformation_type (str): Type of transformation (None, 'rewrite', 'step_back', or 'decompose')\n", "        \n", "    Returns:\n", "        Dict: Results including query, transformed query, context, and response\n", "    \"\"\"\n", "    # Process the document to create a vector store\n", "    vector_store = process_document(pdf_path)\n", "    \n", "    # Apply query transformation and search\n", "    if transformation_type:\n", "        # Perform search with transformed query\n", "        results = transformed_search(query, vector_store, transformation_type)\n", "    else:\n", "        # Perform regular search without transformation\n", "        query_embedding = create_embeddings(query)\n", "        results = vector_store.similarity_search(query_embedding, k=3)\n", "    \n", "    # Combine context from search results\n", "    context = \"\\n\\n\".join([f\"PASSAGE {i+1}:\\n{result['text']}\" for i, result in enumerate(results)])\n", "    \n", "    # Generate response based on the query and combined context\n", "    response = generate_response(query, context)\n", "    \n", "    # Return the results including original query, transformation type, context, and response\n", "    return {\n", "        \"original_query\": query,\n", "        \"transformation_type\": transformation_type,\n", "        \"context\": context,\n", "        \"response\": response\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluating Transformation Techniques"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["\n", "def compare_responses(results, reference_answer, model=\"meta-llama/Llama-3.3-70B-Instruct\"):\n", "    \"\"\"\n", "    Compare responses from different query transformation techniques.\n", "    \n", "    Args:\n", "        results (Dict): Results from different transformation techniques\n", "        reference_answer (str): Reference answer for comparison\n", "        model (str): Model for evaluation\n", "    \"\"\"\n", "    # Define the system prompt to guide the AI assistant's behavior\n", "    system_prompt = \"\"\"You are an expert evaluator of RAG systems. \n", "    Your task is to compare different responses generated using various query transformation techniques \n", "    and determine which technique produced the best response compared to the reference answer.\"\"\"\n", "    \n", "    # Prepare the comparison text with the reference answer and responses from each technique\n", "    comparison_text = f\"\"\"Reference Answer: {reference_answer}\\n\\n\"\"\"\n", "    \n", "    for technique, result in results.items():\n", "        comparison_text += f\"{technique.capitalize()} Query Response:\\n{result['response']}\\n\\n\"\n", "    \n", "    # Define the user prompt with the comparison text\n", "    user_prompt = f\"\"\"\n", "    {comparison_text}\n", "    \n", "    Compare the responses generated by different query transformation techniques to the reference answer.\n", "    \n", "    For each technique (original, rewrite, step_back, decompose):\n", "    1. Score the response from 1-10 based on accuracy, completeness, and relevance\n", "    2. Identify strengths and weaknesses\n", "    \n", "    Then rank the techniques from best to worst and explain which technique performed best overall and why.\n", "    \"\"\"\n", "    \n", "    # Generate the evaluation response using the specified model\n", "    response = client.chat.completions.create(\n", "        model=model,\n", "        temperature=0,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ]\n", "    )\n", "    \n", "    # Print the evaluation results\n", "    print(\"\\n===== EVALUATION RESULTS =====\")\n", "    print(response.choices[0].message.content)\n", "    print(\"=============================\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def evaluate_transformations(pdf_path, query, reference_answer=None):\n", "    \"\"\"\n", "    Evaluate different transformation techniques for the same query.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to PDF document\n", "        query (str): Query to evaluate\n", "        reference_answer (str): Optional reference answer for comparison\n", "        \n", "    Returns:\n", "        Dict: Evaluation results\n", "    \"\"\"\n", "    # Define the transformation techniques to evaluate\n", "    transformation_types = [None, \"rewrite\", \"step_back\", \"decompose\"]\n", "    results = {}\n", "    \n", "    # Run RAG with each transformation technique\n", "    for transformation_type in transformation_types:\n", "        type_name = transformation_type if transformation_type else \"original\"\n", "        print(f\"\\n===== Running RAG with {type_name} query =====\")\n", "        \n", "        # Get the result for the current transformation type\n", "        result = rag_with_query_transformation(pdf_path, query, transformation_type)\n", "        results[type_name] = result\n", "        \n", "        # Print the response for the current transformation type\n", "        print(f\"Response with {type_name} query:\")\n", "        print(result[\"response\"])\n", "        print(\"=\" * 50)\n", "    \n", "    # Compare results if a reference answer is provided\n", "    if reference_answer:\n", "        compare_responses(results, reference_answer)\n", "    \n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation of Query Transformations"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== Running RAG with original query =====\n", "Extracting text from PDF...\n", "Chunking text...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 chunks to the vector store\n", "Response with original query:\n", "Explainable AI (XAI) refers to techniques used to make AI decisions more understandable and transparent. The primary goal of XAI is to enable users to assess the fairness and accuracy of AI systems. This is achieved by providing insights into how AI models make decisions, thereby enhancing trust and accountability in AI systems.\n", "\n", "XAI is considered important for several reasons. Firstly, it addresses concerns about the fairness and accuracy of AI decisions, which is crucial for ensuring that AI systems are reliable and trustworthy. Secondly, XAI helps to establish accountability and responsibility for AI systems, which is essential for addressing potential harms and ensuring ethical behavior.\n", "\n", "Furthermore, XAI is critical in the context of AI development and deployment, as it helps to mitigate the risks associated with AI systems, such as unintended consequences and potential misuse. By making AI systems more transparent and understandable, XAI can help to build trust in AI and ensure that it is developed and deployed in a responsible and ethical manner.\n", "\n", "In the context of various domains, including environmental monitoring, AI-powered systems can be made more transparent and understandable through XAI techniques, which can provide insights into how these systems make decisions and support environmental protection efforts. Similarly, in the development of autonomous weapons systems, XAI can help to address the ethical and security concerns associated with AI-powered weapons.\n", "\n", "Overall, Explainable AI is a crucial area of research that aims to make AI systems more transparent, trustworthy, and accountable. Its importance lies in its ability to enhance trust, accountability, and responsibility in AI systems, while mitigating the risks associated with AI development and deployment.\n", "==================================================\n", "\n", "===== Running RAG with rewrite query =====\n", "Extracting text from PDF...\n", "Chunking text...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 chunks to the vector store\n", "Transformation type: rewrite\n", "Original query: What is 'Explainable AI' and why is it considered important?\n", "Rewritten query: Here's a rewritten query:\n", "\n", "\"What is Explainable AI (XAI) and its significance in the context of machine learning, artificial intelligence, and data science, including its applications, benefits, and limitations, as well as the current state of research and development in this field?\"\n", "\n", "This rewritten query is more specific and detailed because it:\n", "\n", "1. Includes the term \"Explainable AI\" (XAI) to ensure the search results are focused on the specific concept.\n", "2. Provides context by mentioning machine learning, artificial intelligence, and data science, which are relevant fields that XAI is associated with.\n", "3. Specifies the applications, benefits, and limitations of XAI, which will help retrieve information that addresses the user's specific interests.\n", "4. Includes the current state of research and development in XAI, which will provide a more comprehensive understanding of the field.\n", "5. Uses relevant terms and concepts, such as \"XAI,\" \"machine learning,\" \"artificial intelligence,\" and \"data science,\" to help the search engine understand the user's intent and retrieve more accurate results.\n", "\n", "By rewriting the query in this way, the user is more likely to retrieve information that is relevant, accurate, and up-to-date.\n", "Response with rewrite query:\n", "Explainable AI (XAI) is a subfield of artificial intelligence that aims to make AI systems more transparent and understandable. The primary goal of XAI is to provide insights into how AI models make decisions, thereby enhancing trust, accountability, and fairness in AI systems.\n", "\n", "XAI techniques are being developed to explain AI decisions, making it possible for users to assess the reliability and fairness of AI systems. This is crucial in various domains, including environmental monitoring, healthcare, and finance, where AI systems are increasingly being used to make critical decisions.\n", "\n", "The importance of XAI lies in its potential to address several concerns associated with AI, such as:\n", "\n", "1. Lack of transparency: AI systems can be complex and difficult to understand, making it challenging to assess their decision-making processes.\n", "2. Bias and fairness: AI systems can perpetuate biases and unfairness if their decision-making processes are not transparent and explainable.\n", "3. Accountability: XAI can help establish accountability for AI systems, enabling developers, deployers, and users to take responsibility for their actions.\n", "\n", "By making AI systems more transparent and explainable, XAI can help build trust in AI, ensure fairness and accuracy, and mitigate potential risks associated with AI. As AI continues to advance and become more pervasive, the importance of XAI will only grow, and it is likely to play a critical role in shaping the future of AI research and development.\n", "==================================================\n", "\n", "===== Running RAG with step_back query =====\n", "Extracting text from PDF...\n", "Chunking text...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 chunks to the vector store\n", "Transformation type: step_back\n", "Original query: What is 'Explainable AI' and why is it considered important?\n", "Step-back query: Here's a broader, more general version of the original query:\n", "\n", "\"Background information on the concept and significance of Explainable AI in the field of Artificial Intelligence.\"\n", "\n", "This step-back query can help retrieve useful background information on Explainable AI, including its definition, applications, benefits, and challenges, as well as its relevance to the broader field of Artificial Intelligence.\n", "Response with step_back query:\n", "Explainable AI (XAI) is a subfield of artificial intelligence that aims to make AI systems more transparent and understandable. The primary goal of XAI is to provide insights into how AI models make decisions, thereby enhancing trust, accountability, and fairness in AI systems.\n", "\n", "XAI techniques are being developed to explain AI decisions, making it possible for users to assess the reliability and fairness of AI systems. This is crucial in various domains, including environmental monitoring, healthcare, and finance, where AI systems are increasingly being used to make critical decisions.\n", "\n", "The importance of XAI lies in its potential to address several concerns associated with AI, such as:\n", "\n", "1. Lack of transparency: AI systems can be complex and difficult to understand, making it challenging to assess their decision-making processes.\n", "2. Bias and fairness: AI systems can perpetuate biases and unfairness if their decision-making processes are not transparent and explainable.\n", "3. Accountability: XAI can help establish accountability for AI systems, enabling developers, deployers, and users to take responsibility for their actions.\n", "\n", "By making AI systems more transparent and explainable, XAI can help build trust in AI, ensure fairness and accuracy, and mitigate potential risks associated with AI. As AI continues to advance and become more pervasive, the importance of XAI will only grow, and it is likely to play a critical role in shaping the future of AI research and development.\n", "==================================================\n", "\n", "===== Running RAG with decompose query =====\n", "Extracting text from PDF...\n", "Chunking text...\n", "Created 42 text chunks\n", "Creating embeddings for chunks...\n", "Added 42 chunks to the vector store\n", "Transformation type: decompose\n", "Original query: What is 'Explainable AI' and why is it considered important?\n", "Decomposed into sub-queries:\n", "1. What is the definition of 'Explainable AI' and how does it differ from traditional machine learning models?\n", "2. What are the primary goals and objectives of Explainable AI, and how do they align with broader societal needs?\n", "3. What are the key challenges and limitations in developing and deploying Explainable AI systems, and how can they be addressed?\n", "4. How does the importance of Explainable AI relate to broader societal concerns, such as trust, accountability, and fairness in AI decision-making?\n", "Response with decompose query:\n", "Explainable AI (XAI) is a set of techniques aimed at making AI decisions more understandable and transparent. The primary goal of XAI is to enable users to assess the fairness and accuracy of AI systems. This is achieved by providing insights into the decision-making processes of AI systems, thereby enhancing trust and accountability.\n", "\n", "XAI is considered important for several reasons. Firstly, it addresses concerns about the fairness and accuracy of AI systems, which are crucial for building trust in AI. By making AI decisions more understandable, XAI helps users to evaluate the reliability and fairness of AI systems.\n", "\n", "Secondly, XAI is essential for ensuring accountability and responsibility in AI systems. By providing explanations for AI decisions, XAI enables developers, deployers, and users to take ownership of AI systems and address potential harms.\n", "\n", "Lastly, XAI is critical for ensuring the responsible handling of data in AI systems. By making AI systems more transparent, XAI helps to mitigate concerns about privacy and data protection, and ensures that data is handled in a way that is compliant with regulations.\n", "\n", "In summary, Explainable AI is a crucial aspect of building trust in AI, ensuring accountability and responsibility, and promoting responsible data handling. Its importance lies in its ability to make AI systems more transparent, understandable, and accountable, thereby enhancing trust and reliability in AI.\n", "==================================================\n", "\n", "===== EVALUATION RESULTS =====\n", "**Comparison of Query Transformation Techniques**\n", "\n", "I will evaluate the responses generated by different query transformation techniques (original, rewrite, step_back, decompose) and compare them to the reference answer.\n", "\n", "**1. Original Query Response**\n", "\n", "* Score: 8/10\n", "* Strengths:\n", "\t+ Accurately conveys the main idea of Explainable AI (XAI)\n", "\t+ Provides some supporting details about the importance of XAI\n", "* Weaknesses:\n", "\t+ Lacks clarity and concision in some sentences\n", "\t+ Some sentences are wordy and could be rephrased for better clarity\n", "\n", "**2. Rewrite Query Response**\n", "\n", "* Score: 9/10\n", "* Strengths:\n", "\t+ More concise and clear than the original response\n", "\t+ Provides a clearer structure and organization\n", "\t+ Accurately conveys the main idea of XAI\n", "* Weaknesses:\n", "\t+ Some sentences are still wordy and could be rephrased for better clarity\n", "\t+ Lacks supporting details about the importance of XAI\n", "\n", "**3. Step_back Query Response**\n", "\n", "* Score: 8.5/10\n", "* Strengths:\n", "\t+ Provides a clear and concise overview of XAI\n", "\t+ Accurately conveys the main idea of XAI\n", "\t+ Lacks some supporting details about the importance of XAI\n", "* Weaknesses:\n", "\t+ Some sentences are wordy and could be rephrased for better clarity\n", "\t+ Lacks a clear conclusion or summary\n", "\n", "**4. Decompose Query Response**\n", "\n", "* Score: 9.5/10\n", "* Strengths:\n", "\t+ Provides a clear and concise overview of XAI\n", "\t+ Accurately conveys the main idea of XAI\n", "\t+ Provides more supporting details about the importance of XAI\n", "\t+ Lacks some clarity in the introduction\n", "* Weaknesses:\n", "\t+ Some sentences are wordy and could be rephrased for better clarity\n", "\t+ Lacks a clear conclusion or summary\n", "\n", "**Ranking of Techniques**\n", "\n", "1. Decompose Query Response (9.5/10)\n", "2. Rewrite Query Response (9/10)\n", "3. Step_back Query Response (8.5/10)\n", "4. Original Query Response (8/10)\n", "\n", "**Why Decompose Query Response Performed Best**\n", "\n", "The Decompose Query Response performed best overall because it provided a clear and concise overview of XAI, accurately conveying the main idea of the topic. It also provided more supporting details about the importance of XAI, which helped to strengthen the response. Additionally, the Decompose Query Response was well-organized and easy to follow, making it a pleasure to read. While the Rewrite Query Response was close in score, it lacked some supporting details about the importance of XAI, which made it slightly less effective than the Decompose Query Response.\n", "=============================\n"]}], "source": ["# Load the validation data from a JSON file\n", "with open('data/val.json') as f:\n", "    data = json.load(f)\n", "\n", "# Extract the first query from the validation data\n", "query = data[0]['question']\n", "\n", "# Extract the reference answer from the validation data\n", "reference_answer = data[0]['ideal_answer']\n", "\n", "# pdf_path\n", "pdf_path = \"data/AI_Information.pdf\"\n", "\n", "# Run evaluation\n", "evaluation_results = evaluate_transformations(pdf_path, query, reference_answer)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}