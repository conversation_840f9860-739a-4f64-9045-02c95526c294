{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "markdown"}}, "source": ["# Graph RAG: Graph-Enhanced Retrieval-Augmented Generation\n", "\n", "In this notebook, I implement Graph RAG - a technique that enhances traditional RAG systems by organizing knowledge as a connected graph rather than a flat collection of documents. This allows the system to navigate related concepts and retrieve more contextually relevant information than standard vector similarity approaches.\n", "\n", "Key Benefits of Graph RAG\n", "\n", "- Preserves relationships between pieces of information\n", "- Enables traversal through connected concepts to find relevant context\n", "- Improves handling of complex, multi-part queries\n", "- Provides better explainability through visualized knowledge paths"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment\n", "We begin by importing necessary libraries."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import json\n", "import fitz  # PyMuPDF\n", "from openai import OpenAI\n", "from typing import List, Dict, Tuple, Any\n", "import networkx as nx\n", "import matplotlib.pyplot as plt\n", "import heapq\n", "from collections import defaultdict\n", "import re\n", "from PIL import Image\n", "import io"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the OpenAI API Client\n", "We initialize the OpenAI client to generate embeddings and responses."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the OpenAI client with the base URL and API key\n", "client = OpenAI(\n", "    base_url=\"https://api.studio.nebius.com/v1/\",\n", "    api_key=os.getenv(\"OPENAI_API_KEY\")  # Retrieve the API key from environment variables\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Processing Functions"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def extract_text_from_pdf(pdf_path):\n", "    \"\"\"\n", "    Extract text content from a PDF file.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF file\n", "        \n", "    Returns:\n", "        str: Extracted text content\n", "    \"\"\"\n", "    print(f\"Extracting text from {pdf_path}...\")  # Print the path of the PDF being processed\n", "    pdf_document = fitz.open(pdf_path)  # Open the PDF file using PyMuPDF\n", "    text = \"\"  # Initialize an empty string to store the extracted text\n", "    \n", "    # Iterate through each page in the PDF\n", "    for page_num in range(pdf_document.page_count):\n", "        page = pdf_document[page_num]  # Get the page object\n", "        text += page.get_text()  # Extract text from the page and append to the text string\n", "    \n", "    return text  # Return the extracted text content"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def chunk_text(text, chunk_size=1000, overlap=200):\n", "    \"\"\"\n", "    Split text into overlapping chunks.\n", "    \n", "    Args:\n", "        text (str): Input text to chunk\n", "        chunk_size (int): Size of each chunk in characters\n", "        overlap (int): Overlap between chunks in characters\n", "        \n", "    Returns:\n", "        List[Dict]: List of chunks with metadata\n", "    \"\"\"\n", "    chunks = []  # Initialize an empty list to store the chunks\n", "    \n", "    # Iterate over the text with a step size of (chunk_size - overlap)\n", "    for i in range(0, len(text), chunk_size - overlap):\n", "        # Extract a chunk of text from the current position\n", "        chunk_text = text[i:i + chunk_size]\n", "        \n", "        # Ensure we don't add empty chunks\n", "        if chunk_text:\n", "            # Append the chunk with its metadata to the list\n", "            chunks.append({\n", "                \"text\": chunk_text,  # The chunk of text\n", "                \"index\": len(chunks),  # The index of the chunk\n", "                \"start_pos\": i,  # The starting position of the chunk in the original text\n", "                \"end_pos\": i + len(chunk_text)  # The ending position of the chunk in the original text\n", "            })\n", "    \n", "    # Print the number of chunks created\n", "    print(f\"Created {len(chunks)} text chunks\")\n", "    \n", "    return chunks  # Return the list of chunks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating Embeddings"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def create_embeddings(texts, model=\"BAAI/bge-en-icl\"):\n", "    \"\"\"\n", "    Create embeddings for the given texts.\n", "    \n", "    Args:\n", "        texts (List[str]): Input texts\n", "        model (str): Embedding model name\n", "        \n", "    Returns:\n", "        List[List[float]]: Embedding vectors\n", "    \"\"\"\n", "    # Handle empty input\n", "    if not texts:\n", "        return []\n", "        \n", "    # Process in batches if needed (OpenAI API limits)\n", "    batch_size = 100\n", "    all_embeddings = []\n", "    \n", "    # Iterate over the input texts in batches\n", "    for i in range(0, len(texts), batch_size):\n", "        batch = texts[i:i + batch_size]  # Get the current batch of texts\n", "        \n", "        # Create embeddings for the current batch\n", "        response = client.embeddings.create(\n", "            model=model,\n", "            input=batch\n", "        )\n", "        \n", "        # Extract embeddings from the response\n", "        batch_embeddings = [item.embedding for item in response.data]\n", "        all_embeddings.extend(batch_embeddings)  # Add the batch embeddings to the list\n", "    \n", "    return all_embeddings  # Return all embeddings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Knowledge Graph Construction"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def extract_concepts(text):\n", "    \"\"\"\n", "    Extract key concepts from text using OpenAI's API.\n", "    \n", "    Args:\n", "        text (str): Text to extract concepts from\n", "        \n", "    Returns:\n", "        List[str]: List of concepts\n", "    \"\"\"\n", "    # System message to instruct the model on what to do\n", "    system_message = \"\"\"Extract key concepts and entities from the provided text.\n", "Return ONLY a list of 5-10 key terms, entities, or concepts that are most important in this text.\n", "Format your response as a JSON array of strings.\"\"\"\n", "\n", "    # Make a request to the OpenAI API\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_message},\n", "            {\"role\": \"user\", \"content\": f\"Extract key concepts from:\\n\\n{text[:3000]}\"}  # Limit for API\n", "        ],\n", "        temperature=0.0,\n", "        response_format={\"type\": \"json_object\"}\n", "    )\n", "    \n", "    try:\n", "        # Parse concepts from the response\n", "        concepts_json = json.loads(response.choices[0].message.content)\n", "        concepts = concepts_json.get(\"concepts\", [])\n", "        if not concepts and \"concepts\" not in concepts_json:\n", "            # Try to get any array in the response\n", "            for key, value in concepts_json.items():\n", "                if isinstance(value, list):\n", "                    concepts = value\n", "                    break\n", "        return concepts\n", "    except (json.<PERSON><PERSON><PERSON><PERSON>, AttributeError):\n", "        # Fallback if JSON parsing fails\n", "        content = response.choices[0].message.content\n", "        # Try to extract anything that looks like a list\n", "        matches = re.findall(r'\\[(.*?)\\]', content, re.DOTALL)\n", "        if matches:\n", "            items = re.findall(r'\"([^\"]*)\"', matches[0])\n", "            return items\n", "        return []"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def build_knowledge_graph(chunks):\n", "    \"\"\"\n", "    Build a knowledge graph from text chunks.\n", "    \n", "    Args:\n", "        chunks (List[Dict]): List of text chunks with metadata\n", "        \n", "    Returns:\n", "        Tuple[nx.<PERSON>, List[np.n<PERSON><PERSON>]]: The knowledge graph and chunk embeddings\n", "    \"\"\"\n", "    print(\"Building knowledge graph...\")\n", "    \n", "    # Create a graph\n", "    graph = nx.Graph()\n", "    \n", "    # Extract chunk texts\n", "    texts = [chunk[\"text\"] for chunk in chunks]\n", "    \n", "    # Create embeddings for all chunks\n", "    print(\"Creating embeddings for chunks...\")\n", "    embeddings = create_embeddings(texts)\n", "    \n", "    # Add nodes to the graph\n", "    print(\"Adding nodes to the graph...\")\n", "    for i, chunk in enumerate(chunks):\n", "        # Extract concepts from the chunk\n", "        print(f\"Extracting concepts for chunk {i+1}/{len(chunks)}...\")\n", "        concepts = extract_concepts(chunk[\"text\"])\n", "        \n", "        # Add node with attributes\n", "        graph.add_node(i, \n", "                      text=chunk[\"text\"], \n", "                      concepts=concepts,\n", "                      embedding=embeddings[i])\n", "    \n", "    # Connect nodes based on shared concepts\n", "    print(\"Creating edges between nodes...\")\n", "    for i in range(len(chunks)):\n", "        node_concepts = set(graph.nodes[i][\"concepts\"])\n", "        \n", "        for j in range(i + 1, len(chunks)):\n", "            # Calculate concept overlap\n", "            other_concepts = set(graph.nodes[j][\"concepts\"])\n", "            shared_concepts = node_concepts.intersection(other_concepts)\n", "            \n", "            # If they share concepts, add an edge\n", "            if shared_concepts:\n", "                # Calculate semantic similarity using embeddings\n", "                similarity = np.dot(embeddings[i], embeddings[j]) / (np.linalg.norm(embeddings[i]) * np.linalg.norm(embeddings[j]))\n", "                \n", "                # Calculate edge weight based on concept overlap and semantic similarity\n", "                concept_score = len(shared_concepts) / min(len(node_concepts), len(other_concepts))\n", "                edge_weight = 0.7 * similarity + 0.3 * concept_score\n", "                \n", "                # Only add edges with significant relationship\n", "                if edge_weight > 0.6:\n", "                    graph.add_edge(i, j, \n", "                                  weight=edge_weight,\n", "                                  similarity=similarity,\n", "                                  shared_concepts=list(shared_concepts))\n", "    \n", "    print(f\"Knowledge graph built with {graph.number_of_nodes()} nodes and {graph.number_of_edges()} edges\")\n", "    return graph, embeddings"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Graph Traversal and Query Processing"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def traverse_graph(query, graph, embeddings, top_k=5, max_depth=3):\n", "    \"\"\"\n", "    Traverse the knowledge graph to find relevant information for the query.\n", "    \n", "    Args:\n", "        query (str): The user's question\n", "        graph (nx.Graph): The knowledge graph\n", "        embeddings (List): List of node embeddings\n", "        top_k (int): Number of initial nodes to consider\n", "        max_depth (int): Maximum traversal depth\n", "        \n", "    Returns:\n", "        List[Dict]: Relevant information from graph traversal\n", "    \"\"\"\n", "    print(f\"Traversing graph for query: {query}\")\n", "    \n", "    # Get query embedding\n", "    query_embedding = create_embeddings(query)\n", "    \n", "    # Calculate similarity between query and all nodes\n", "    similarities = []\n", "    for i, node_embedding in enumerate(embeddings):\n", "        similarity = np.dot(query_embedding, node_embedding) / (np.linalg.norm(query_embedding) * np.linalg.norm(node_embedding))\n", "        similarities.append((i, similarity))\n", "    \n", "    # Sort by similarity (descending)\n", "    similarities.sort(key=lambda x: x[1], reverse=True)\n", "    \n", "    # Get top-k most similar nodes as starting points\n", "    starting_nodes = [node for node, _ in similarities[:top_k]]\n", "    print(f\"Starting traversal from {len(starting_nodes)} nodes\")\n", "    \n", "    # Initialize traversal\n", "    visited = set()  # Set to keep track of visited nodes\n", "    traversal_path = []  # List to store the traversal path\n", "    results = []  # List to store the results\n", "    \n", "    # Use a priority queue for traversal\n", "    queue = []\n", "    for node in starting_nodes:\n", "        heapq.heappush(queue, (-similarities[node][1], node))  # Negative for max-heap\n", "    \n", "    # Traverse the graph using a modified breadth-first search with priority\n", "    while queue and len(results) < (top_k * 3):  # Limit results to top_k * 3\n", "        _, node = heapq.heappop(queue)\n", "        \n", "        if node in visited:\n", "            continue\n", "        \n", "        # <PERSON> as visited\n", "        visited.add(node)\n", "        traversal_path.append(node)\n", "        \n", "        # Add current node's text to results\n", "        results.append({\n", "            \"text\": graph.nodes[node][\"text\"],\n", "            \"concepts\": graph.nodes[node][\"concepts\"],\n", "            \"node_id\": node\n", "        })\n", "        \n", "        # Explore neighbors if we haven't reached max depth\n", "        if len(traversal_path) < max_depth:\n", "            neighbors = [(neighbor, graph[node][neighbor][\"weight\"]) \n", "                        for neighbor in graph.neighbors(node)\n", "                        if neighbor not in visited]\n", "            \n", "            # Add neighbors to queue based on edge weight\n", "            for neighbor, weight in sorted(neighbors, key=lambda x: x[1], reverse=True):\n", "                heapq.heappush(queue, (-weight, neighbor))\n", "    \n", "    print(f\"Graph traversal found {len(results)} relevant chunks\")\n", "    return results, traversal_path"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Response Generation"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def generate_response(query, context_chunks):\n", "    \"\"\"\n", "    Generate a response using the retrieved context.\n", "    \n", "    Args:\n", "        query (str): The user's question\n", "        context_chunks (List[Dict]): Relevant chunks from graph traversal\n", "        \n", "    Returns:\n", "        str: Generated response\n", "    \"\"\"\n", "    # Extract text from each chunk in the context\n", "    context_texts = [chunk[\"text\"] for chunk in context_chunks]\n", "    \n", "    # Combine the extracted texts into a single context string, separated by \"---\"\n", "    combined_context = \"\\n\\n---\\n\\n\".join(context_texts)\n", "    \n", "    # Define the maximum allowed length for the context (OpenAI limit)\n", "    max_context = 14000\n", "    \n", "    # Truncate the combined context if it exceeds the maximum length\n", "    if len(combined_context) > max_context:\n", "        combined_context = combined_context[:max_context] + \"... [truncated]\"\n", "    \n", "    # Define the system message to guide the AI assistant\n", "    system_message = \"\"\"You are a helpful AI assistant. Answer the user's question based on the provided context.\n", "If the information is not in the context, say so. Refer to specific parts of the context in your answer when possible.\"\"\"\n", "\n", "    # Generate the response using the OpenAI API\n", "    response = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",  # Specify the model to use\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_message},  # System message to guide the assistant\n", "            {\"role\": \"user\", \"content\": f\"Context:\\n{combined_context}\\n\\nQuestion: {query}\"}  # User message with context and query\n", "        ],\n", "        temperature=0.2  # Set the temperature for response generation\n", "    )\n", "    \n", "    # Return the generated response content\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def visualize_graph_traversal(graph, traversal_path):\n", "    \"\"\"\n", "    Visualize the knowledge graph and the traversal path.\n", "    \n", "    Args:\n", "        graph (nx.Graph): The knowledge graph\n", "        traversal_path (List): List of nodes in traversal order\n", "    \"\"\"\n", "    plt.figure(figsize=(12, 10))  # Set the figure size\n", "    \n", "    # Define node colors, default to light blue\n", "    node_color = ['lightblue'] * graph.number_of_nodes()\n", "    \n", "    # Highlight traversal path nodes in light green\n", "    for node in traversal_path:\n", "        node_color[node] = 'lightgreen'\n", "    \n", "    # Highlight start node in green and end node in red\n", "    if traversal_path:\n", "        node_color[traversal_path[0]] = 'green'\n", "        node_color[traversal_path[-1]] = 'red'\n", "    \n", "    # Create positions for all nodes using spring layout\n", "    pos = nx.spring_layout(graph, k=0.5, iterations=50, seed=42)\n", "    \n", "    # Draw the graph nodes\n", "    nx.draw_networkx_nodes(graph, pos, node_color=node_color, node_size=500, alpha=0.8)\n", "    \n", "    # Draw edges with width proportional to weight\n", "    for u, v, data in graph.edges(data=True):\n", "        weight = data.get('weight', 1.0)\n", "        nx.draw_networkx_edges(graph, pos, edgelist=[(u, v)], width=weight*2, alpha=0.6)\n", "    \n", "    # Draw traversal path with red dashed lines\n", "    traversal_edges = [(traversal_path[i], traversal_path[i+1]) \n", "                      for i in range(len(traversal_path)-1)]\n", "    \n", "    nx.draw_networkx_edges(graph, pos, edgelist=traversal_edges, \n", "                          width=3, alpha=0.8, edge_color='red', \n", "                          style='dashed', arrows=True)\n", "    \n", "    # Add labels with the first concept for each node\n", "    labels = {}\n", "    for node in graph.nodes():\n", "        concepts = graph.nodes[node]['concepts']\n", "        label = concepts[0] if concepts else f\"Node {node}\"\n", "        labels[node] = f\"{node}: {label}\"\n", "    \n", "    nx.draw_networkx_labels(graph, pos, labels=labels, font_size=8)\n", "    \n", "    plt.title(\"Knowledge Graph with Traversal Path\")  # Set the plot title\n", "    plt.axis('off')  # Turn off the axis\n", "    plt.tight_layout()  # Adjust layout\n", "    plt.show()  # Display the plot"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complete Graph RAG Pipeline"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def graph_rag_pipeline(pdf_path, query, chunk_size=1000, chunk_overlap=200, top_k=3):\n", "    \"\"\"\n", "    Complete Graph RAG pipeline from document to answer.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF document\n", "        query (str): The user's question\n", "        chunk_size (int): Size of text chunks\n", "        chunk_overlap (int): Overlap between chunks\n", "        top_k (int): Number of top nodes to consider for traversal\n", "        \n", "    Returns:\n", "        Dict: Results including answer and graph visualization data\n", "    \"\"\"\n", "    # Extract text from the PDF document\n", "    text = extract_text_from_pdf(pdf_path)\n", "    \n", "    # Split the extracted text into overlapping chunks\n", "    chunks = chunk_text(text, chunk_size, chunk_overlap)\n", "    \n", "    # Build a knowledge graph from the text chunks\n", "    graph, embeddings = build_knowledge_graph(chunks)\n", "    \n", "    # Traverse the knowledge graph to find relevant information for the query\n", "    relevant_chunks, traversal_path = traverse_graph(query, graph, embeddings, top_k)\n", "    \n", "    # Generate a response based on the query and the relevant chunks\n", "    response = generate_response(query, relevant_chunks)\n", "    \n", "    # Visualize the graph traversal path\n", "    visualize_graph_traversal(graph, traversal_path)\n", "    \n", "    # Return the query, response, relevant chunks, traversal path, and the graph\n", "    return {\n", "        \"query\": query,\n", "        \"response\": response,\n", "        \"relevant_chunks\": relevant_chunks,\n", "        \"traversal_path\": traversal_path,\n", "        \"graph\": graph\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation Function"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def evaluate_graph_rag(pdf_path, test_queries, reference_answers=None):\n", "    \"\"\"\n", "    Evaluate Graph RAG on multiple test queries.\n", "    \n", "    Args:\n", "        pdf_path (str): Path to the PDF document\n", "        test_queries (List[str]): List of test queries\n", "        reference_answers (List[str], optional): Reference answers for comparison\n", "        \n", "    Returns:\n", "        Dict: Evaluation results\n", "    \"\"\"\n", "    # Extract text from PDF\n", "    text = extract_text_from_pdf(pdf_path)\n", "    \n", "    # Split text into chunks\n", "    chunks = chunk_text(text)\n", "    \n", "    # Build knowledge graph (do this once for all queries)\n", "    graph, embeddings = build_knowledge_graph(chunks)\n", "    \n", "    results = []\n", "    \n", "    for i, query in enumerate(test_queries):\n", "        print(f\"\\n\\n=== Evaluating Query {i+1}/{len(test_queries)} ===\")\n", "        print(f\"Query: {query}\")\n", "        \n", "        # Traverse graph to find relevant information\n", "        relevant_chunks, traversal_path = traverse_graph(query, graph, embeddings)\n", "        \n", "        # Generate response\n", "        response = generate_response(query, relevant_chunks)\n", "        \n", "        # Compare with reference answer if available\n", "        reference = None\n", "        comparison = None\n", "        if reference_answers and i < len(reference_answers):\n", "            reference = reference_answers[i]\n", "            comparison = compare_with_reference(response, reference, query)\n", "        \n", "        # Append results for the current query\n", "        results.append({\n", "            \"query\": query,\n", "            \"response\": response,\n", "            \"reference_answer\": reference,\n", "            \"comparison\": comparison,\n", "            \"traversal_path_length\": len(traversal_path),\n", "            \"relevant_chunks_count\": len(relevant_chunks)\n", "        })\n", "        \n", "        # Display results\n", "        print(f\"\\nResponse: {response}\\n\")\n", "        if comparison:\n", "            print(f\"Comparison: {comparison}\\n\")\n", "    \n", "    # Return evaluation results and graph statistics\n", "    return {\n", "        \"results\": results,\n", "        \"graph_stats\": {\n", "            \"nodes\": graph.number_of_nodes(),\n", "            \"edges\": graph.number_of_edges(),\n", "            \"avg_degree\": sum(dict(graph.degree()).values()) / graph.number_of_nodes()\n", "        }\n", "    }"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def compare_with_reference(response, reference, query):\n", "    \"\"\"\n", "    Compare generated response with reference answer.\n", "    \n", "    Args:\n", "        response (str): Generated response\n", "        reference (str): Reference answer\n", "        query (str): Original query\n", "        \n", "    Returns:\n", "        str: Comparison analysis\n", "    \"\"\"\n", "    # System message to instruct the model on how to compare the responses\n", "    system_message = \"\"\"Compare the AI-generated response with the reference answer.\n", "Evaluate based on: correctness, completeness, and relevance to the query.\n", "Provide a brief analysis (2-3 sentences) of how well the generated response matches the reference.\"\"\"\n", "\n", "    # Construct the prompt with the query, AI-generated response, and reference answer\n", "    prompt = f\"\"\"\n", "Query: {query}\n", "\n", "AI-generated response:\n", "{response}\n", "\n", "Reference answer:\n", "{reference}\n", "\n", "How well does the AI response match the reference?\n", "\"\"\"\n", "\n", "    # Make a request to the OpenAI API to generate the comparison analysis\n", "    comparison = client.chat.completions.create(\n", "        model=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_message},  # System message to guide the assistant\n", "            {\"role\": \"user\", \"content\": prompt}  # User message with the prompt\n", "        ],\n", "        temperature=0.0  # Set the temperature for response generation\n", "    )\n", "    \n", "    # Return the generated comparison analysis\n", "    return comparison.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation of Graph RAG on a Sample PDF Document"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting text from data/AI_Information.pdf...\n", "Created 42 text chunks\n", "Building knowledge graph...\n", "Creating embeddings for chunks...\n", "Adding nodes to the graph...\n", "Extracting concepts for chunk 1/42...\n", "Extracting concepts for chunk 2/42...\n", "Extracting concepts for chunk 3/42...\n", "Extracting concepts for chunk 4/42...\n", "Extracting concepts for chunk 5/42...\n", "Extracting concepts for chunk 6/42...\n", "Extracting concepts for chunk 7/42...\n", "Extracting concepts for chunk 8/42...\n", "Extracting concepts for chunk 9/42...\n", "Extracting concepts for chunk 10/42...\n", "Extracting concepts for chunk 11/42...\n", "Extracting concepts for chunk 12/42...\n", "Extracting concepts for chunk 13/42...\n", "Extracting concepts for chunk 14/42...\n", "Extracting concepts for chunk 15/42...\n", "Extracting concepts for chunk 16/42...\n", "Extracting concepts for chunk 17/42...\n", "Extracting concepts for chunk 18/42...\n", "Extracting concepts for chunk 19/42...\n", "Extracting concepts for chunk 20/42...\n", "Extracting concepts for chunk 21/42...\n", "Extracting concepts for chunk 22/42...\n", "Extracting concepts for chunk 23/42...\n", "Extracting concepts for chunk 24/42...\n", "Extracting concepts for chunk 25/42...\n", "Extracting concepts for chunk 26/42...\n", "Extracting concepts for chunk 27/42...\n", "Extracting concepts for chunk 28/42...\n", "Extracting concepts for chunk 29/42...\n", "Extracting concepts for chunk 30/42...\n", "Extracting concepts for chunk 31/42...\n", "Extracting concepts for chunk 32/42...\n", "Extracting concepts for chunk 33/42...\n", "Extracting concepts for chunk 34/42...\n", "Extracting concepts for chunk 35/42...\n", "Extracting concepts for chunk 36/42...\n", "Extracting concepts for chunk 37/42...\n", "Extracting concepts for chunk 38/42...\n", "Extracting concepts for chunk 39/42...\n", "Extracting concepts for chunk 40/42...\n", "Extracting concepts for chunk 41/42...\n", "Extracting concepts for chunk 42/42...\n", "Creating edges between nodes...\n", "Knowledge graph built with 42 nodes and 110 edges\n", "Traversing graph for query: What are the key applications of transformers in natural language processing?\n", "Starting traversal from 3 nodes\n", "Graph traversal found 9 relevant chunks\n"]}, {"data": {"image/png": "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********************************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******************************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", "text/plain": ["<Figure size 1200x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== ANSWER ===\n", "The provided context does not mention transformers in natural language processing. The context discusses various topics such as machine learning, deep learning, reinforcement learning, and AI applications, but transformers are not mentioned.\n", "Extracting text from data/AI_Information.pdf...\n", "Created 42 text chunks\n", "Building knowledge graph...\n", "Creating embeddings for chunks...\n", "Adding nodes to the graph...\n", "Extracting concepts for chunk 1/42...\n", "Extracting concepts for chunk 2/42...\n", "Extracting concepts for chunk 3/42...\n", "Extracting concepts for chunk 4/42...\n", "Extracting concepts for chunk 5/42...\n", "Extracting concepts for chunk 6/42...\n", "Extracting concepts for chunk 7/42...\n", "Extracting concepts for chunk 8/42...\n", "Extracting concepts for chunk 9/42...\n", "Extracting concepts for chunk 10/42...\n", "Extracting concepts for chunk 11/42...\n", "Extracting concepts for chunk 12/42...\n", "Extracting concepts for chunk 13/42...\n", "Extracting concepts for chunk 14/42...\n", "Extracting concepts for chunk 15/42...\n", "Extracting concepts for chunk 16/42...\n", "Extracting concepts for chunk 17/42...\n", "Extracting concepts for chunk 18/42...\n", "Extracting concepts for chunk 19/42...\n", "Extracting concepts for chunk 20/42...\n", "Extracting concepts for chunk 21/42...\n", "Extracting concepts for chunk 22/42...\n", "Extracting concepts for chunk 23/42...\n", "Extracting concepts for chunk 24/42...\n", "Extracting concepts for chunk 25/42...\n", "Extracting concepts for chunk 26/42...\n", "Extracting concepts for chunk 27/42...\n", "Extracting concepts for chunk 28/42...\n", "Extracting concepts for chunk 29/42...\n", "Extracting concepts for chunk 30/42...\n", "Extracting concepts for chunk 31/42...\n", "Extracting concepts for chunk 32/42...\n", "Extracting concepts for chunk 33/42...\n", "Extracting concepts for chunk 34/42...\n", "Extracting concepts for chunk 35/42...\n", "Extracting concepts for chunk 36/42...\n", "Extracting concepts for chunk 37/42...\n", "Extracting concepts for chunk 38/42...\n", "Extracting concepts for chunk 39/42...\n", "Extracting concepts for chunk 40/42...\n", "Extracting concepts for chunk 41/42...\n", "Extracting concepts for chunk 42/42...\n", "Creating edges between nodes...\n", "Knowledge graph built with 42 nodes and 107 edges\n", "\n", "\n", "=== Evaluating Query 1/1 ===\n", "Query: How do transformers handle sequential data compared to RNNs?\n", "Traversing graph for query: How do transformers handle sequential data compared to RNNs?\n", "Starting traversal from 5 nodes\n", "Graph traversal found 9 relevant chunks\n", "\n", "Response: The provided context does not specifically discuss transformers. However, I can provide a general comparison between transformers and RNNs.\n", "\n", "Transformers are a type of neural network architecture that have gained popularity in recent years, particularly in natural language processing tasks. They are designed to handle sequential data, but they do so in a different way compared to RNNs.\n", "\n", "RNNs are designed to process sequential data by maintaining a hidden state that captures information from previous time steps. This allows RNNs to learn patterns and relationships in sequential data, such as text or time series.\n", "\n", "Transformers, on the other hand, use self-attention mechanisms to process sequential data. Instead of using a hidden state to capture information, transformers use attention weights to weigh the importance of different input elements at different positions in the sequence. This allows transformers to capture long-range dependencies in sequential data more effectively than RNNs.\n", "\n", "In terms of handling sequential data, transformers are generally more efficient and effective than RNNs, particularly for tasks such as language translation, text generation, and sentiment analysis. However, transformers do require more computational resources and training data than RNNs, which can be a limitation in certain applications.\n", "\n", "It's worth noting that the context does not provide a detailed comparison between transformers and RNNs, but I can provide a general overview of the differences between these two architectures.\n", "\n", "Comparison: Comparison of the AI-generated response with the reference answer:\n", "\n", "* Correctness: The AI response is mostly correct, but it lacks the specific detail about vanishing gradient problems in RNNs, which is mentioned in the reference answer.\n", "* Completeness: The AI response provides a more comprehensive overview of the differences between transformers and RNNs, including their strengths and limitations.\n", "* Relevance to the query: The AI response is highly relevant to the query, as it directly compares the handling of sequential data by transformers and RNNs.\n", "\n", "Brief analysis: The AI response provides a more detailed and comprehensive comparison between transformers and RNNs, but it could benefit from the specific detail about vanishing gradient problems in RNNs to make it more accurate. Overall, the AI response is a good match for the reference answer, but with some minor improvements.\n", "\n", "\n", "=== EVALUATION SUMMARY ===\n", "Graph nodes: 42\n", "Graph edges: 107\n", "\n", "Query 1: How do transformers handle sequential data compared to RNNs?\n", "Path length: 9\n", "Chunks used: 9\n"]}], "source": ["# Path to the PDF document containing AI information\n", "pdf_path = \"data/AI_Information.pdf\"\n", "\n", "# Define an AI-related query for testing Graph RAG\n", "query = \"What are the key applications of transformers in natural language processing?\"\n", "\n", "# Execute the Graph RAG pipeline to process the document and answer the query\n", "results = graph_rag_pipeline(pdf_path, query)\n", "\n", "# Print the response generated from the Graph RAG system\n", "print(\"\\n=== ANSWER ===\")\n", "print(results[\"response\"])\n", "\n", "# Define a test query and reference answer for formal evaluation\n", "test_queries = [\n", "    \"How do transformers handle sequential data compared to RNNs?\"\n", "]\n", "\n", "# Reference answer for evaluation purposes\n", "reference_answers = [\n", "    \"Transformers handle sequential data differently from RNNs by using self-attention mechanisms instead of recurrent connections. This allows transformers to process all tokens in parallel rather than sequentially, capturing long-range dependencies more efficiently and enabling better parallelization during training. Unlike RNNs, transformers don't suffer from vanishing gradient problems with long sequences.\"\n", "]\n", "\n", "# Run formal evaluation of the Graph RAG system with the test query\n", "evaluation = evaluate_graph_rag(pdf_path, test_queries, reference_answers)\n", "\n", "# Print evaluation summary statistics\n", "print(\"\\n=== EVALUATION SUMMARY ===\")\n", "print(f\"Graph nodes: {evaluation['graph_stats']['nodes']}\")\n", "print(f\"Graph edges: {evaluation['graph_stats']['edges']}\")\n", "for i, result in enumerate(evaluation['results']):\n", "    print(f\"\\nQuery {i+1}: {result['query']}\")\n", "    print(f\"Path length: {result['traversal_path_length']}\")\n", "    print(f\"Chunks used: {result['relevant_chunks_count']}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}